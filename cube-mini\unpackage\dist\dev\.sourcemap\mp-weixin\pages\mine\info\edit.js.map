{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/mine/info/edit.vue?b7c7", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/mine/info/edit.vue?f0ba", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/mine/info/edit.vue?3e28", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/mine/info/edit.vue?a20e", "uni-app:///pages/mine/info/edit.vue", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/mine/info/edit.vue?8a1c", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/mine/info/edit.vue?4f01"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "user", "nick<PERSON><PERSON>", "phonenumber", "email", "sex", "avatar", "sexs", "text", "value", "rules", "required", "errorMessage", "onLoad", "onReady", "methods", "onChooseAvatar", "console", "filePath", "name", "url", "success", "fail", "getUser", "submit", "storage"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AAC4K;AAC5K,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,oVAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA8oB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC6BlqB;AAMA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;MACAC;QACAR;UACAQ;YACAC;YACAC;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACAC;MACA;MACAtB;QACAuB;QACAC;QACAC;QACAC;UACA;UACA;UACA;UACA;UACA;UACA;QAEA;QACAC;UACAL;QACA;MACA;IAEA;IACAM;MAAA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACAP;MACA;QACA;UACAQ;UACAA;UACAR;UACAA;UACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnHA;AAAA;AAAA;AAAA;AAA6tC,CAAgB,6lCAAG,EAAC,C;;;;;;;;;;;ACAjvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/info/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/mine/info/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=6f7d5570&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/info/edit.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=6f7d5570&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item\" */ \"@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <view class=\"example\">\r\n\r\n      <uni-forms ref=\"form\" :model=\"user\" labelWidth=\"80px\" :rules=\"rules\">\r\n        <button class=\"avatar-wrapper\" open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\">\r\n          <image v-if=\"user.avatar\" :src=\"user.avatar\" class=\"cu-avatar xl round\" mode=\"widthFix\"></image>\r\n          <text v-if=\"!user.avatar\" class=\"loginLogoText\">授权头像</text>\r\n        </button>\r\n\r\n        <uni-forms-item label=\"用户昵称\" name=\"nickName\">\r\n          <uni-easyinput  type='nickname' v-model=\"user.nickName\" placeholder=\"请输入昵称\" />\r\n        </uni-forms-item>\r\n        <!-- <uni-forms-item label=\"手机号码\" name=\"phonenumber\">\r\n\t\t\t\t\t<uni-easyinput v-model=\"user.phonenumber\" placeholder=\"请输入手机号码\" />\r\n\t\t\t\t</uni-forms-item>\r\n\t\t\t\t<uni-forms-item label=\"邮箱\" name=\"email\">\r\n\t\t\t\t\t<uni-easyinput v-model=\"user.email\" placeholder=\"请输入邮箱\" />\r\n\t\t\t\t</uni-forms-item> -->\r\n        <!--  <uni-forms-item label=\"性别\" name=\"sex\" required>\r\n          <uni-data-checkbox v-model=\"user.sex\" :localdata=\"sexs\" />\r\n        </uni-forms-item> -->\r\n      </uni-forms>\r\n      <button type=\"primary\" class=\"login-btn cu-btn block bg-green lg round\" @click=\"submit\">提交</button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import {\r\n    getUserProfile\r\n  } from \"@/api/system/user\"\r\n  import {\r\n    updateUserProfile\r\n  } from \"@/api/system/user\"\r\n  import storage from '@/utils/storage'\r\n  import constant from '@/utils/constant'\r\n  import config from '@/config'\r\n\r\n  export default {\r\n    data() {\r\n      return {\r\n        user: {\r\n          nickName: \"\",\r\n          phonenumber: \"\",\r\n          email: \"\",\r\n          sex: \"\",\r\n          avatar: \"\"\r\n        },\r\n        sexs: [{\r\n          text: '男',\r\n          value: \"0\"\r\n        }, {\r\n          text: '女',\r\n          value: \"1\"\r\n        }],\r\n        rules: {\r\n          nickName: {\r\n            rules: [{\r\n              required: true,\r\n              errorMessage: '用户昵称不能为空'\r\n            }]\r\n          }\r\n        }\r\n      }\r\n    },\r\n    onLoad() {\r\n      this.getUser()\r\n    },\r\n    onReady() {\r\n      this.$refs.form.setRules(this.rules)\r\n    },\r\n    methods: {\r\n      //获取微信头像\r\n      onChooseAvatar(e) {\r\n        console.log(\"头像地址：\" + e.detail.avatarUrl)\r\n        const baseUrl = config.baseUrl\r\n        wx.uploadFile({\r\n          filePath: e.detail.avatarUrl,\r\n          name: 'file',\r\n          url: baseUrl + \"/common/upload\",\r\n          success: (res) => {\r\n            // 拿到一个服务器地址，永久地址\r\n            // JSON 解析，将JSON字符串解析为JSON对象\r\n            const resObj = JSON.parse(res.data);\r\n            // 拿到一个服务器地址，永久地址\r\n            //将永久地址存到 newAvatar 进行显示和存储信息\r\n            this.user.avatar = resObj.url\r\n\r\n          },\r\n          fail: function(res) {\r\n            console.log(res); //发送失败回调，可以在这里了解失败原因\r\n          }\r\n        })\r\n\r\n      },\r\n      getUser() {\r\n        getUserProfile().then(response => {\r\n          this.user = response.data\r\n        })\r\n      },\r\n      submit(ref) {\r\n        console.log(this.user.nickName, \"this.user.nickName\");\r\n        this.$refs.form.validate().then(res => {\r\n          updateUserProfile(this.user).then(response => {\r\n            storage.set(constant.name, this.user.nickName)\r\n            storage.set(constant.avatar, this.user.avatar)\r\n            console.log(\"修改后\" + storage.get(constant.name))\r\n            console.log(\"修改后\" + storage.get(constant.avatar))\r\n            this.$modal.msgSuccess(\"修改成功\")\r\n            this.$tab.reLaunch('/pages/mine/index')\r\n          })\r\n        })\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n  page {\r\n    background-color: #ffffff;\r\n  }\r\n\r\n  .cu-avatar {\r\n    // border: 2px solid #eaeaea;\r\n\r\n    .icon {\r\n      font-size: 40px;\r\n    }\r\n  }\r\n\r\n  .example {\r\n    padding: 15px;\r\n    background-color: #fff;\r\n  }\r\n\r\n  .segmented-control {\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .button-group {\r\n    margin-top: 15px;\r\n    display: flex;\r\n    justify-content: space-around;\r\n  }\r\n\r\n  .form-item {\r\n    display: flex;\r\n    align-items: center;\r\n    flex: 1;\r\n  }\r\n\r\n  .avatar-wrapper {\r\n    background-color: white;\r\n    margin-bottom: 50rpx;\r\n  }\r\n\r\n  .avatar-wrapper::after {\r\n    background-color: white;\r\n    border: 0;\r\n    display: block;\r\n    height: 200rpx;\r\n    margin-top: 150rpx;\r\n  }\r\n\r\n  .button {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 35px;\r\n    line-height: 35px;\r\n    margin-left: 10px;\r\n  }\r\n\r\n  .login-btn {\r\n    margin-top: 80rpx;\r\n    height: 90rpx !important;\r\n    background-color: #1ec57c !important;\r\n  }\r\n</style>", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754066559532\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}