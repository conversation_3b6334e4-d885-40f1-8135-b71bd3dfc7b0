{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/user/points/index.vue?b5b4", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/user/points/index.vue?e07a", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/user/points/index.vue?673a", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/user/points/index.vue?7400", "uni-app:///pages/user/points/index.vue", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/user/points/index.vue?87f6", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/user/points/index.vue?62d3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "triggered", "selectedIndex", "tabItems", "name", "listItems", "total", "hasMore", "pointsForm", "userId", "pageSize", "pageIndex", "start", "end", "type", "selectedDateRange", "minDate", "maxDate", "created", "computed", "windowHeight", "methods", "onPulling", "setTimeout", "that", "onRefresh", "console", "uni", "onScrollToLower", "loadMore", "getUserPointsRecord", "updateDateRanges", "onTabItemClick", "onStartDateChange", "onEndDateChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC4K;AAC5K,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+oB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgEnqB;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,EACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAH;QAAA;QACAC;MACA;;MACAG;MAAA;MACAC;IACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IAEAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;QACA;QACA;QACAC;UACAC;QACA;MACA;IACA;IACAC;MAAA;MACAC;MACA;MACA;MACA;QACA;UACA;UACAC;UACAD;UACA;QACA;MACA;IACA;IACAE;MAEA;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACAf;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAgB;MAAA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpOA;AAAA;AAAA;AAAA;AAA8tC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAlvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/points/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/user/points/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=ce3f877e&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/points/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=ce3f877e&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view  :style=\"{height: `${windowHeight}px`}\">\r\n\r\n\t\t<view>\r\n\t\t\t<view class=\"top\">\r\n\t\t\t\t<!-- 时间范围筛选框 -->\r\n\t\t\t\t<view class=\"filter-box\">\r\n\t\t\t\t\t<picker mode=\"date\" :value=\"selectedDateRange.start\" :start=\"minDate\" :end=\"maxDate\"\r\n\t\t\t\t\t\t@change=\"onStartDateChange\">\r\n\t\t\t\t\t\t<view class=\"picker\">\r\n\t\t\t\t\t\t\t{{ selectedDateRange.start }} 至\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t\t<picker mode=\"date\" :value=\"selectedDateRange.end\" :start=\"selectedDateRange.start\" :end=\"maxDate\"\r\n\t\t\t\t\t\t@change=\"onEndDateChange\">\r\n\t\t\t\t\t\t<view class=\"picker\">\r\n\t\t\t\t\t\t\t{{ selectedDateRange.end }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- Tab导航 -->\r\n\t\t\t\t<view class=\"tab-nav\">\r\n\t\t\t\t\t<view class=\"tab-item\" v-for=\"(item, index) in tabItems\" :key=\"index\"\r\n\t\t\t\t\t\t:class=\"{ active: selectedIndex === index }\" @click=\"onTabItemClick(index)\">\r\n\t\t\t\t\t\t{{ item.name }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 列表 -->\r\n\r\n\t\t\t\t<scroll-view scroll-y class=\"list\" @scrolltolower=\"onScrollToLower\" refresher-enabled :refresher-triggered=\"triggered\" @refresherpulling=\"onPulling\"\r\n\t\t\t\t\t@refresherrefresh=\"onRefresh\">\r\n\r\n\t\t\t\t\t<view class=\"list-item\" v-for=\"(item, index) in listItems\" :key=\"index\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t<!-- <view class=\"point-text\">\r\n\t\t\t\t\t\t\t<text v-if=\"item.corp_name\">{{ item.corp_name }}</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<view class=\"point-text\" style=\"display:flex;justify-content: space-between;\">\r\n\t\t\t\t\t\t<!-- \t<text>{{ item.nick_name }}</text> -->\r\n\t\t\t\t\t\t<text>{{ item.change_type }}</text> \r\n\t\t\t\t\t\t\t<text :style=\"{color:item.change_amount >= 0 ? 'green' : 'red',fontSize: '35rpx'}\">{{ item.change_amount }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"point-text\">\r\n\t\t\t\t\t\t\t<!-- <text style=\"color: #8f8f94\">{{ item.change_type }}</text> -->\r\n\t\t\t\t\t\t\t<text style=\"color: #8f8f94\">{{ item.create_time }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"!hasMore\" class=\"end-text\">\r\n\t\t\t\t\t\t已经到底啦~\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetUserPointsRecord\r\n\t} from '@/api/report'\r\n\timport storage from '@/utils/storage'\r\n\timport constant from '@/utils/constant'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttriggered: false,\r\n\t\t\t\tselectedIndex: 0,\r\n\t\t\t\ttabItems: [{\r\n\t\t\t\t\t\tname: '全部'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '积分消费'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '积分充值'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tlistItems: [],\r\n\t\t\t\ttotal: '',\r\n\t\t\t\thasMore: true,\r\n\t\t\t\tpointsForm: {\r\n\t\t\t\t\tuserId: storage.get(constant.userId),\r\n\t\t\t\t\tpageSize: 10,\r\n\t\t\t\t\tpageIndex: 1,\r\n\t\t\t\t\tstart: '',\r\n\t\t\t\t\tend: '',\r\n\t\t\t\t\ttype: ''\r\n\t\t\t\t},\r\n\t\t\t\tselectedDateRange: {\r\n\t\t\t\t\tstart: '2023-01-01', // 初始开始日期\r\n\t\t\t\t\tend: '2023-01-01' // 初始结束日期\r\n\t\t\t\t},\r\n\t\t\t\tminDate: '', // 最小日期\r\n\t\t\t\tmaxDate: '' // 最大日期\r\n\t\t\t}\r\n\t\t},\r\n\t\t // 分享到微信好友\r\n\t\t    // onShareAppMessage() {\r\n\t\t    //   return {\r\n\t\t    //     title: '我的积分',\r\n\t\t    //     path: '/pages/user/points/index',\r\n\t\t    //     imageUrl: '',\r\n\t\t    //   }\r\n\t\t    // },\r\n\t\t    // 分享到朋友圈\r\n\t\t    // onShareTimeline() {\r\n\t\t    //   return {\r\n\t\t    //     title: '我的积分',\r\n\t\t    //     path: '/pages/user/points/index',\r\n\t\t    //     imageUrl: '',\r\n\t\t    //   }\r\n\t\t    // },\r\n\t\tcreated() {\r\n\t\t\t// wx.showShareMenu({\r\n\t\t\t// \twithShareTicket: true,\r\n\t\t\t// \tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t// \t});\r\n\t\t\tthis.updateDateRanges();\r\n\t\t\tthis.getUserPointsRecord();\r\n\t\t},\r\n\t\tcomputed: {\r\n\r\n\t\t\twindowHeight() {\r\n\t\t\t\treturn uni.getSystemInfoSync().windowHeight - 50\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonPulling() {\r\n\t\t\t            var that = this;\r\n\t\t\t            if(!this.triggered){\r\n\t\t\t                //下拉加载，先让其变true再变false才能关闭\r\n\t\t\t                this.triggered = true; \r\n\t\t\t                //关闭加载状态 (转动的圈)，需要一点延时才能关闭\r\n\t\t\t                setTimeout(() => {\r\n\t\t\t                    that.triggered = false;\r\n\t\t\t                },1000)\r\n\t\t\t            }\r\n\t\t\t        },\r\n\t\t\tonRefresh() {\r\n\t\t\t\tconsole.log(\"下拉刷新\");\r\n\t\t\t\tthis.hasMore = true;\r\n\t\t\t\tthis.pointsForm.pageIndex = 1;\r\n\t\t\tgetUserPointsRecord(this.pointsForm).then(res => {\r\n\t\t\t\t\t\tif (res.data && res.data.list) {\r\n\t\t\t\t\t\t\tthis.listItems = res.data.list;\r\n\t\t\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\t\t\tconsole.log(\"关闭刷新\");\r\n\t\t\t\t\t\t\tthis.total = res.data.total;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonScrollToLower() {\r\n\r\n\t\t\t\tif (this.hasMore) {\r\n\t\t\t\t\tthis.loadMore();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tloadMore() {\r\n\t\t\t\tif (this.pointsForm.pageIndex * this.pointsForm.pageSize >= this.total) {\r\n\t\t\t\t\tthis.hasMore = false;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.pointsForm.pageIndex++;\r\n\t\t\t\tthis.getUserPointsRecord();\r\n\t\t\t},\r\n\t\t\tgetUserPointsRecord() {\r\n\t\t\t\tgetUserPointsRecord(this.pointsForm).then(res => {\r\n\t\t\t\t\tif (res.data && res.data.list) {\r\n\t\t\t\t\t\tthis.listItems = this.listItems.concat(res.data.list);\r\n\t\t\t\t\t\tthis.total = res.data.total;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\tupdateDateRanges() {\r\n\t\t\t\tconst today = new Date();\r\n\t\t\t\tconst minDate = new Date(today);\r\n\t\t\t\tminDate.setMonth(today.getMonth() - 1); // 设置为一个月前的今天\r\n\t\t\t\tthis.minDate = minDate.toISOString().split('T')[0]; // 转换为 YYYY-MM-DD 格式\r\n\t\t\t\tthis.maxDate = today.toISOString().split('T')[0]; // 转换为 YYYY-MM-DD 格式\r\n\t\t\t\tthis.selectedDateRange.start = this.minDate;\r\n\t\t\t\tthis.pointsForm.start = this.minDate;\r\n\t\t\t\tthis.selectedDateRange.end = this.maxDate;\r\n\t\t\t\tthis.pointsForm.end = this.maxDate;\r\n\t\t\t},\r\n\t\t\tonTabItemClick(index) {\r\n\t\t\t\tthis.selectedIndex = index;\r\n\t\t\t\t\r\n\t\t\t\tif(index==1){\r\n\t\t\t\t\tthis.pointsForm.type = 2\r\n\t\t\t\t}else if(index==2) {\r\n\t\t\t\t\tthis.pointsForm.type = 1\r\n\t\t\t\t}else {\r\n\t\t\t\t\tthis.pointsForm.type = ''\r\n\t\t\t\t}\r\n\t\t\t\tgetUserPointsRecord(this.pointsForm).then(res => {\r\n\t\t\t\t\t\t\tif (res.data && res.data.list) {\r\n\t\t\t\t\t\t\t\tthis.listItems = res.data.list;\r\n\t\t\t\t\t\t\t\tthis.total = res.data.total;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonStartDateChange(e) {\r\n\t\t\t\tthis.selectedDateRange.start = e.detail.value;\r\n\t\t\t\t// 确保结束日期不早于开始日期\r\n\t\t\t\tif (this.selectedDateRange.end < this.selectedDateRange.start) {\r\n\t\t\t\t\tthis.selectedDateRange.end = this.selectedDateRange.start;\r\n\t\t\t\t}\r\n\t\t\t\tthis.pointsForm.start = this.selectedDateRange.start;\r\n\t\t\t\t// console.log('开始日期:', this.selectedDateRange.start);\r\n\t\t\t},\r\n\t\t\tonEndDateChange(e) {\r\n\t\t\t\tthis.selectedDateRange.end = e.detail.value;\r\n\t\t\t\t// 确保开始日期不晚于结束日期\r\n\t\t\t\tif (this.selectedDateRange.start > this.selectedDateRange.end) {\r\n\t\t\t\t\tthis.selectedDateRange.start = this.selectedDateRange.end;\r\n\t\t\t\t}\r\n\t\t\t\tthis.pointsForm.end = this.selectedDateRange.end;\r\n\t\t\t\t// console.log('结束日期:', this.selectedDateRange.end);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: #f8f8f8;\r\n\t}\r\n\r\n\t.end-text {\r\n\t\ttext-align: center;\r\n\t\tpadding: 20rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\r\n\r\n\t.filter-box {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 10rpx;\r\n\t\tbackground-color: white;\r\n\t\r\n\t}\r\n\r\n\t.picker {\r\n\t\tpadding: 10rpx;\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 5rpx;\r\n\t}\r\n\r\n\t.tab-nav {\r\n\t\tdisplay: flex;\r\n\t\twidth: 100%;\r\n\t\tjustify-content: space-around;\r\n\t\tpadding: 10rpx 0;\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 5rpx;\r\n\t}\r\n\r\n\t.tab-item {\r\n\t\tpadding: 10rpx;\r\n\t\tposition: relative;\r\n\t\tcolor: #000;\r\n\t}\r\n\r\n\t.tab-item.active {\r\n\t\tcolor: royalblue;\r\n\t}\r\n\r\n\t.tab-item.active::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\twidth: 50%;\r\n\t\theight: 2rpx;\r\n\t\tbackground-color: blue;\r\n\t}\r\n\r\n\t.list {\r\n\t\theight: 87vh;\r\n\t\t/* 根据实际情况调整高度 */\r\n\t\toverflow-y: auto;\r\n\t\t/* 确保内容超出时可以滚动 */\r\n\t\twidth: 99%;\r\n\t\tpadding: 10rpx;\r\n\t\tborder-radius: 5rpx;\r\n\t}\r\n\r\n\t.list-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmargin-bottom: 20rpx;\r\n\t    background-color: white;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\r\n\t.point-text {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754060940192\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}