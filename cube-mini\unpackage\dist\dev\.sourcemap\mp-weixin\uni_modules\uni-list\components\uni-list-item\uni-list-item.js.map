{"version": 3, "sources": ["webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/uni_modules/uni-list/components/uni-list-item/uni-list-item.vue?cdf9", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/uni_modules/uni-list/components/uni-list-item/uni-list-item.vue?031e", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/uni_modules/uni-list/components/uni-list-item/uni-list-item.vue?2f08", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/uni_modules/uni-list/components/uni-list-item/uni-list-item.vue?3fcc", "uni-app:///uni_modules/uni-list/components/uni-list-item/uni-list-item.vue", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/uni_modules/uni-list/components/uni-list-item/uni-list-item.vue?1192", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/uni_modules/uni-list/components/uni-list-item/uni-list-item.vue?bca9"], "names": ["name", "emits", "props", "direction", "type", "default", "title", "note", "ellipsis", "disabled", "clickable", "showArrow", "link", "to", "showBadge", "showSwitch", "switchChecked", "badgeText", "badgeType", "badgeStyle", "rightText", "thumb", "thumbSize", "showExtraIcon", "extraIcon", "color", "size", "customPrefix", "border", "customStyle", "padding", "backgroundColor", "keepScrollPosition", "watch", "handler", "verticalPadding", "horizontalPadding", "topPadding", "rightPadding", "bottomPadding", "leftPadding", "immediate", "data", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "top", "right", "bottom", "left", "mounted", "methods", "getForm", "parent", "parentName", "onClick", "onSwitchChange", "openPage", "pageApi", "url", "success", "fail", "uni"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACc;;;AAG1E;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAsqB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+C1rB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjCA,gBAkCA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACAc;MACAf;MACAC;QACA;MACA;IACA;IACAe;MACAhB;MACAC;IACA;IACAgB;MACAjB;MACAC;IACA;IACAiB;MACAlB;MACAC;IACA;IACAkB;MACAnB;MACAC;IACA;IACAmB;MACApB;MACAC;QACA;UACAD;UACAqB;UACAC;UACAC;QACA;MACA;IACA;IACAC;MACAxB;MACAC;IACA;IACAwB;MACAzB;MACAC;QACA;UACAyB;UACAC;QACA;MACA;IACA;IACAC;MACA5B;MACAC;IACA;EACA;EACA4B;IACA;MACAC;QACA;UACAJ;QACA;QACA;QACA;UACA;UACA;YACA;YACA;YACA;YACA;UACA;QACA;UACA;YAAAK;YAAAC;UACA;YACA;YACA;YACA;YACA;UACA;QACA;UACA;YAAAC;YAAAC;YAAAC;YAAAC;UACA;YACA;YACA;YACA;YACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACA;EACAC;IACA;MACAC;MACAb;QACAc;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;QACAC;QACA;QACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;QACA;UACAX;QACA;MACA;IACA;IACAY;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACAC;QACAC;UACA;YACAhB;UACA;QACA;QACAiB;UACA;YACAjB;UACA;QACA;MACA;MACA;QACA;UACAkB;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;UACAA;MAAA;IAEA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC7TA;AAAA;AAAA;AAAA;AAAiwC,CAAgB,smCAAG,EAAC,C;;;;;;;;;;;ACArxC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-list/components/uni-list-item/uni-list-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-list-item.vue?vue&type=template&id=296a3d7e&\"\nvar renderjs\nimport script from \"./uni-list-item.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-list-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-list-item.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-list/components/uni-list-item/uni-list-item.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-list-item.vue?vue&type=template&id=296a3d7e&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniBadge: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-badge/components/uni-badge/uni-badge\" */ \"@/uni_modules/uni-badge/components/uni-badge/uni-badge.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-list-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-list-item.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- #ifdef APP-NVUE -->\r\n\t<cell :keep-scroll-position=\"keepScrollPosition\">\r\n\t\t<!-- #endif -->\r\n\t\t<view :class=\"{ 'uni-list-item--disabled': disabled }\" :style=\"{'background-color':customStyle.backgroundColor}\"\r\n\t\t\t:hover-class=\"(!clickable && !link) || disabled || showSwitch ? '' : 'uni-list-item--hover'\"\r\n\t\t\tclass=\"uni-list-item\" @click=\"onClick\">\r\n\t\t\t<view v-if=\"!isFirstChild\" class=\"border--left\" :class=\"{ 'uni-list--border': border }\"></view>\r\n\t\t\t<view class=\"uni-list-item__container\"\r\n\t\t\t\t:class=\"{ 'container--right': showArrow || link, 'flex--direction': direction === 'column'}\"\r\n\t\t\t\t:style=\"{paddingTop:padding.top,paddingLeft:padding.left,paddingRight:padding.right,paddingBottom:padding.bottom}\">\r\n\t\t\t\t<slot name=\"header\">\r\n\t\t\t\t\t<view class=\"uni-list-item__header\">\r\n\t\t\t\t\t\t<view v-if=\"thumb\" class=\"uni-list-item__icon\">\r\n\t\t\t\t\t\t\t<image :src=\"thumb\" class=\"uni-list-item__icon-img\" :class=\"['uni-list--' + thumbSize]\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-else-if=\"showExtraIcon\" class=\"uni-list-item__icon\">\r\n\t\t\t\t\t\t\t<uni-icons :customPrefix=\"extraIcon.customPrefix\" :color=\"extraIcon.color\" :size=\"extraIcon.size\" :type=\"extraIcon.type\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</slot>\r\n\t\t\t\t<slot name=\"body\">\r\n\t\t\t\t\t<view class=\"uni-list-item__content\"\r\n\t\t\t\t\t\t:class=\"{ 'uni-list-item__content--center': thumb || showExtraIcon || showBadge || showSwitch }\">\r\n\t\t\t\t\t\t<text v-if=\"title\" class=\"uni-list-item__content-title\"\r\n\t\t\t\t\t\t\t:class=\"[ellipsis !== 0 && ellipsis <= 2 ? 'uni-ellipsis-' + ellipsis : '']\">{{ title }}</text>\r\n\t\t\t\t\t\t<text v-if=\"note\" class=\"uni-list-item__content-note\">{{ note }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</slot>\r\n\t\t\t\t<slot name=\"footer\">\r\n\t\t\t\t\t<view v-if=\"rightText || showBadge || showSwitch\" class=\"uni-list-item__extra\"\r\n\t\t\t\t\t\t:class=\"{ 'flex--justify': direction === 'column' }\">\r\n\t\t\t\t\t\t<text v-if=\"rightText\" class=\"uni-list-item__extra-text\">{{ rightText }}</text>\r\n\t\t\t\t\t\t<uni-badge v-if=\"showBadge\" :type=\"badgeType\" :text=\"badgeText\" :custom-style=\"badgeStyle\" />\r\n\t\t\t\t\t\t<switch v-if=\"showSwitch\" :disabled=\"disabled\" :checked=\"switchChecked\"\r\n\t\t\t\t\t\t\t@change=\"onSwitchChange\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t\t<uni-icons v-if=\"showArrow || link\" :size=\"16\" class=\"uni-icon-wrapper\" color=\"#bbb\" type=\"arrowright\" />\r\n\t\t</view>\r\n\t\t<!-- #ifdef APP-NVUE -->\r\n\t</cell>\r\n\t<!-- #endif -->\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * ListItem 列表子组件\r\n\t * @description 列表子组件\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=24\r\n\t * @property {String} \ttitle \t\t\t\t\t\t\t标题\r\n\t * @property {String} \tnote \t\t\t\t\t\t\t描述\r\n\t * @property {String} \tthumb \t\t\t\t\t\t\t左侧缩略图，若thumb有值，则不会显示扩展图标\r\n\t * @property {String}  \tthumbSize = [lg|base|sm]\t\t略缩图大小\r\n\t * \t@value \t lg\t\t\t大图\r\n\t * \t@value \t base\t\t一般\r\n\t * \t@value \t sm\t\t\t小图\r\n\t * @property {String} \tbadgeText\t\t\t\t\t\t数字角标内容\r\n\t * @property {String} \tbadgeType \t\t\t\t\t\t数字角标类型，参考[uni-icons](https://ext.dcloud.net.cn/plugin?id=21)\r\n\t * @property {Object}   badgeStyle           数字角标样式\r\n\t * @property {String} \trightText \t\t\t\t\t\t右侧文字内容\r\n\t * @property {Boolean} \tdisabled = [true|false]\t\t\t是否禁用\r\n\t * @property {Boolean} \tclickable = [true|false] \t\t是否开启点击反馈\r\n\t * @property {String} \tlink = [navigateTo|redirectTo|reLaunch|switchTab] 是否展示右侧箭头并开启点击反馈\r\n\t *  @value \tnavigateTo \t同 uni.navigateTo()\r\n\t * \t@value redirectTo \t同 uni.redirectTo()\r\n\t * \t@value reLaunch   \t同 uni.reLaunch()\r\n\t * \t@value switchTab  \t同 uni.switchTab()\r\n\t * @property {String | PageURIString} \tto  \t\t\t跳转目标页面\r\n\t * @property {Boolean} \tshowBadge = [true|false] \t\t是否显示数字角标\r\n\t * @property {Boolean} \tshowSwitch = [true|false] \t\t是否显示Switch\r\n\t * @property {Boolean} \tswitchChecked = [true|false] \tSwitch是否被选中\r\n\t * @property {Boolean} \tshowExtraIcon = [true|false] \t左侧是否显示扩展图标\r\n\t * @property {Object} \textraIcon \t\t\t\t\t\t扩展图标参数，格式为 {color: '#4cd964',size: '22',type: 'spinner'}\r\n\t * @property {String} \tdirection = [row|column]\t\t排版方向\r\n\t * @value row \t\t\t水平排列\r\n\t * @value column \t\t垂直排列\r\n\t * @event {Function} \tclick \t\t\t\t\t\t\t点击 uniListItem 触发事件\r\n\t * @event {Function} \tswitchChange \t\t\t\t\t点击切换 Switch 时触发\r\n\t */\r\n\texport default {\r\n\t\tname: 'UniListItem',\r\n\t\temits: ['click', 'switchChange'],\r\n\t\tprops: {\r\n\t\t\tdirection: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'row'\r\n\t\t\t},\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tnote: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tellipsis: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tdisabled: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tclickable: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tshowArrow: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tlink: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tto: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tshowBadge: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tshowSwitch: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tswitchChecked: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tbadgeText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tbadgeType: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'success'\r\n\t\t\t},\r\n\t\t\tbadgeStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\trightText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tthumb: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tthumbSize: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'base'\r\n\t\t\t},\r\n\t\t\tshowExtraIcon: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\textraIcon: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\ttype: '',\r\n\t\t\t\t\t\tcolor: '#000000',\r\n\t\t\t\t\t\tsize: 20,\r\n\t\t\t\t\t\tcustomPrefix: ''\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tborder: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tcustomStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tpadding: '',\r\n\t\t\t\t\t\tbackgroundColor: '#FFFFFF'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tkeepScrollPosition: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t'customStyle.padding': {\r\n\t\t\t\thandler(padding) {\r\n\t\t\t\t\tif(typeof padding == 'number'){\r\n\t\t\t\t\t\tpadding += ''\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlet paddingArr = padding.split(' ')\r\n\t\t\t\t\tif (paddingArr.length === 1) {\r\n\t\t\t\t\t\tconst allPadding = paddingArr[0]\r\n\t\t\t\t\t\tthis.padding = {\r\n\t\t\t\t\t\t\t\"top\": allPadding,\r\n\t\t\t\t\t\t\t\"right\": allPadding,\r\n\t\t\t\t\t\t\t\"bottom\": allPadding,\r\n\t\t\t\t\t\t\t\"left\": allPadding\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (paddingArr.length === 2) {\r\n\t\t\t\t\t\tconst [verticalPadding, horizontalPadding] = paddingArr;\r\n\t\t\t\t\t\tthis.padding = {\r\n\t\t\t\t\t\t\t\"top\": verticalPadding,\r\n\t\t\t\t\t\t\t\"right\": horizontalPadding,\r\n\t\t\t\t\t\t\t\"bottom\": verticalPadding,\r\n\t\t\t\t\t\t\t\"left\": horizontalPadding\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (paddingArr.length === 4) {\r\n\t\t\t\t\t\t\tconst [topPadding, rightPadding, bottomPadding, leftPadding] = paddingArr;\r\n\t\t\t\t\t\t\tthis.padding = {\r\n\t\t\t\t\t\t\t\t\"top\": topPadding,\r\n\t\t\t\t\t\t\t\t\"right\": rightPadding,\r\n\t\t\t\t\t\t\t\t\"bottom\": bottomPadding,\r\n\t\t\t\t\t\t\t\t\"left\": leftPadding\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t}\r\n\t\t},\r\n\t\t// inject: ['list'],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisFirstChild: false,\r\n\t\t\t\tpadding: {\r\n\t\t\t\t\ttop: \"\",\r\n\t\t\t\t\tright: \"\",\r\n\t\t\t\t\tbottom: \"\",\r\n\t\t\t\t\tleft: \"\"\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.list = this.getForm()\r\n\t\t\t// 判断是否存在 uni-list 组件\r\n\t\t\tif (this.list) {\r\n\t\t\t\tif (!this.list.firstChildAppend) {\r\n\t\t\t\t\tthis.list.firstChildAppend = true;\r\n\t\t\t\t\tthis.isFirstChild = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 获取父元素实例\r\n\t\t\t */\r\n\t\t\tgetForm(name = 'uniList') {\r\n\t\t\t\tlet parent = this.$parent;\r\n\t\t\t\tlet parentName = parent.$options.name;\r\n\t\t\t\twhile (parentName !== name) {\r\n\t\t\t\t\tparent = parent.$parent;\r\n\t\t\t\t\tif (!parent) return false\r\n\t\t\t\t\tparentName = parent.$options.name;\r\n\t\t\t\t}\r\n\t\t\t\treturn parent;\r\n\t\t\t},\r\n\t\t\tonClick() {\r\n\t\t\t\tif (this.to !== '') {\r\n\t\t\t\t\tthis.openPage();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.clickable || this.link) {\r\n\t\t\t\t\tthis.$emit('click', {\r\n\t\t\t\t\t\tdata: {}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonSwitchChange(e) {\r\n\t\t\t\tthis.$emit('switchChange', e.detail);\r\n\t\t\t},\r\n\t\t\topenPage() {\r\n\t\t\t\tif (['navigateTo', 'redirectTo', 'reLaunch', 'switchTab'].indexOf(this.link) !== -1) {\r\n\t\t\t\t\tthis.pageApi(this.link);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.pageApi('navigateTo');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpageApi(api) {\r\n\t\t\t\tlet callback = {\r\n\t\t\t\t\turl: this.to,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tthis.$emit('click', {\r\n\t\t\t\t\t\t\tdata: res\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: err => {\r\n\t\t\t\t\t\tthis.$emit('click', {\r\n\t\t\t\t\t\t\tdata: err\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tswitch (api) {\r\n\t\t\t\t\tcase 'navigateTo':\r\n\t\t\t\t\t\tuni.navigateTo(callback)\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tcase 'redirectTo':\r\n\t\t\t\t\t\tuni.redirectTo(callback)\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tcase 'reLaunch':\r\n\t\t\t\t\t\tuni.reLaunch(callback)\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tcase 'switchTab':\r\n\t\t\t\t\t\tuni.switchTab(callback)\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\tuni.navigateTo(callback)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t$uni-font-size-sm:12px;\r\n\t$uni-font-size-base:14px;\r\n\t$uni-font-size-lg:16px;\r\n\t$uni-spacing-col-lg: 12px;\r\n\t$uni-spacing-row-lg: 15px;\r\n\t$uni-img-size-sm:20px;\r\n\t$uni-img-size-base:26px;\r\n\t$uni-img-size-lg:40px;\r\n\t$uni-border-color:#e5e5e5;\r\n\t$uni-bg-color-hover:#f1f1f1;\r\n\t$uni-text-color-grey:#999;\r\n\t$list-item-pd: $uni-spacing-col-lg $uni-spacing-row-lg;\r\n\r\n\t.uni-list-item {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tfont-size: $uni-font-size-lg;\r\n\t\tposition: relative;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #fff;\r\n\t\tflex-direction: row;\r\n\t\t/* #ifdef H5 */\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-list-item--disabled {\r\n\t\topacity: 0.3;\r\n\t}\r\n\r\n\t.uni-list-item--hover {\r\n\t\tbackground-color: $uni-bg-color-hover !important;\r\n\t}\r\n\r\n\t.uni-list-item__container {\r\n\t\tposition: relative;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tpadding: $list-item-pd;\r\n\t\tpadding-left: $uni-spacing-row-lg;\r\n\t\tflex: 1;\r\n\t\toverflow: hidden;\r\n\t\t// align-items: center;\r\n\t}\r\n\r\n\t.container--right {\r\n\t\tpadding-right: 0;\r\n\t}\r\n\r\n\t// .border--left {\r\n\t// \tmargin-left: $uni-spacing-row-lg;\r\n\t// }\r\n\t.uni-list--border {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tleft: 0;\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tborder-top-color: $uni-border-color;\r\n\t\tborder-top-style: solid;\r\n\t\tborder-top-width: 0.5px;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\t.uni-list--border:after {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tleft: 0;\r\n\t\theight: 1px;\r\n\t\tcontent: '';\r\n\t\t-webkit-transform: scaleY(0.5);\r\n\t\ttransform: scaleY(0.5);\r\n\t\tbackground-color: $uni-border-color;\r\n\t}\r\n\r\n\t/* #endif */\r\n\t.uni-list-item__content {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tpadding-right: 8px;\r\n\t\tflex: 1;\r\n\t\tcolor: #3b4144;\r\n\t\t// overflow: hidden;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: space-between;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-list-item__content--center {\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.uni-list-item__content-title {\r\n\t\tfont-size: $uni-font-size-base;\r\n\t\tcolor: #3b4144;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-list-item__content-note {\r\n\t\tmargin-top: 6rpx;\r\n\t\tcolor: $uni-text-color-grey;\r\n\t\tfont-size: $uni-font-size-sm;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-list-item__extra {\r\n\t\t// width: 25%;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: flex-end;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.uni-list-item__header {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.uni-list-item__icon {\r\n\t\tmargin-right: 18rpx;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.uni-list-item__icon-img {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: block;\r\n\t\t/* #endif */\r\n\t\theight: $uni-img-size-base;\r\n\t\twidth: $uni-img-size-base;\r\n\t\tmargin-right: 10px;\r\n\t}\r\n\r\n\t.uni-icon-wrapper {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\talign-items: center;\r\n\t\tpadding: 0 10px;\r\n\t}\r\n\r\n\t.flex--direction {\r\n\t\tflex-direction: column;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\talign-items: initial;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.flex--justify {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tjustify-content: initial;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-list--lg {\r\n\t\theight: $uni-img-size-lg;\r\n\t\twidth: $uni-img-size-lg;\r\n\t}\r\n\r\n\t.uni-list--base {\r\n\t\theight: $uni-img-size-base;\r\n\t\twidth: $uni-img-size-base;\r\n\t}\r\n\r\n\t.uni-list--sm {\r\n\t\theight: $uni-img-size-sm;\r\n\t\twidth: $uni-img-size-sm;\r\n\t}\r\n\r\n\t.uni-list-item__extra-text {\r\n\t\tcolor: $uni-text-color-grey;\r\n\t\tfont-size: $uni-font-size-sm;\r\n\t}\r\n\r\n\t.uni-ellipsis-1 {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\toverflow: hidden;\r\n\t\twhite-space: nowrap;\r\n\t\ttext-overflow: ellipsis;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tlines: 1;\r\n\t\ttext-overflow: ellipsis;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-ellipsis-2 {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tlines: 2;\r\n\t\ttext-overflow: ellipsis;\r\n\t\t/* #endif */\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-list-item.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-list-item.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754060940231\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}