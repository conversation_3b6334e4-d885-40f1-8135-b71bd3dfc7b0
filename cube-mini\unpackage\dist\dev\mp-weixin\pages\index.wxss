
page {
	height: 100%;
}
.container {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #F5F7FA;
}
.swiper-container {
	flex-shrink: 0;
	height: 400rpx;
	background-color: #fff;
}
.swiper {
	width: 100%;
	height: 100%;
}
.swiper-image {
	width: 100%;
	height: 100%;
}
.content {
	flex: 1;
	overflow: auto;
}
.section {
	margin: 30rpx 20rpx;
	background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
	border-radius: 24rpx;
	padding: 50rpx 40rpx;
	box-shadow:
		0 8rpx 32rpx rgba(0, 0, 0, 0.04),
		0 2rpx 16rpx rgba(0, 0, 0, 0.02);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	position: relative;
	overflow: hidden;
}
.section::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3rpx;
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
	opacity: 0.6;
}
.section-title {
	display: flex;
	align-items: center;
	margin-bottom: 50rpx;
	position: relative;
	z-index: 2;
}
.title-text {
	margin-left: 20rpx;
	font-size: 22px;
	font-weight: 700;
	color: #2d3748;
	letter-spacing: 0.8px;
	position: relative;
}
.title-text::after {
	content: '';
	position: absolute;
	bottom: -8rpx;
	left: 0;
	width: 60rpx;
	height: 4rpx;
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
	border-radius: 2rpx;
	opacity: 0.7;
}
.feature-cards {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
	gap: 30rpx;
	margin-top: 20rpx;
}
.feature-card {
	background: linear-gradient(145deg, #ffffff 0%, #f8faff 100%);
	padding: 40rpx 30rpx 35rpx;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	overflow: hidden;
	box-shadow:
		0 8rpx 24rpx rgba(0, 0, 0, 0.06),
		0 2rpx 8rpx rgba(0, 0, 0, 0.04);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.feature-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 6rpx;
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
	opacity: 0.8;
}
.feature-card::after {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: radial-gradient(circle, rgba(102, 126, 234, 0.03) 0%, transparent 70%);
	opacity: 0;
	transition: opacity 0.4s ease;
}
.feature-card:hover {
	-webkit-transform: translateY(-8rpx) scale(1.01);
	        transform: translateY(-8rpx) scale(1.01);
	box-shadow:
		0 16rpx 32rpx rgba(102, 126, 234, 0.12),
		0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	border-color: rgba(102, 126, 234, 0.15);
}
.feature-card:hover::after {
	opacity: 1;
}
.feature-icon {
	width: 72rpx;
	height: 72rpx;
	margin-bottom: 24rpx;
	border-radius: 14rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
	position: relative;
	z-index: 2;
}
.feature-card:hover .feature-icon {
	-webkit-transform: translateY(-4rpx) scale(1.05);
	        transform: translateY(-4rpx) scale(1.05);
	box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.15);
}
.feature-title {
	font-size: 16px;
	font-weight: 600;
	color: #2d3748;
	margin-bottom: 12rpx;
	letter-spacing: 0.3px;
	text-align: center;
	position: relative;
	z-index: 2;
	line-height: 1.3;
}
.feature-desc {
	font-size: 13px;
	color: #64748b;
	text-align: center;
	line-height: 1.6;
	max-width: 220rpx;
	position: relative;
	z-index: 2;
	opacity: 0.85;
}
.advantage-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.advantage-item {
	display: flex;
	align-items: flex-start;
	padding: 20rpx;
	background-color: #F8F9FF;
	border-radius: 12rpx;
}
.advantage-icon-wrapper {
	flex-shrink: 0;
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #E6EFFF;
	border-radius: 50%;
}
.advantage-content {
	margin-left: 20rpx;
}
.advantage-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}
.advantage-desc {
	font-size: 14px;
	color: #666;
}
.scenario-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}
.scenario-card {
	flex: 1;
	min-width: 280rpx;
	background-color: #fff;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.scenario-image {
	width: 100%;
	height: 200rpx;
}
.scenario-info {
	padding: 20rpx;
}
.scenario-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}
.scenario-desc {
	font-size: 14px;
	color: #666;
}

