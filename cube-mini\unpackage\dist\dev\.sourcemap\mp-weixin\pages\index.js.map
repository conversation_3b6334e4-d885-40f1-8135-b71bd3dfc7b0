{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/index.vue?8e43", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/index.vue?98c3", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/index.vue?2691", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/index.vue?6c24", "uni-app:///pages/index.vue", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/index.vue?1230", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/index.vue?d391"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "banners", "image", "features", "name", "avatar", "icon", "title", "description", "type", "advantages", "scenarios"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAinB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2DroB;EACAC;IACA;MACAC;QACAC;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,EACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAL;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAL;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAL;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA,CACA;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AClJA;AAAA;AAAA;AAAA;AAA04B,CAAgB,o2BAAG,EAAC,C;;;;;;;;;;;ACA95B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2a183b29&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2a183b29&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"swiper-container\">\r\n\t\t\t<swiper class=\"swiper\" circular :indicator-dots=\"true\" :autoplay=\"true\" interval=\"3000\" duration=\"500\">\r\n\t\t\t\t<swiper-item v-for=\"(item, index) in banners\" :key=\"index\">\r\n\t\t\t\t\t<image :src=\"item.image\" mode=\"aspectFill\" class=\"swiper-image\" />\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t\t<scroll-view class=\"content\" scroll-y>\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<view class=\"section-title\" style=\"margin-left: 150rpx;\">\r\n\t\t\t\t\t<uni-icons type=\"star\" size=\"24\" color=\"#4169E1\" />\r\n\t\t\t\t\t<text class=\"title-text\">优立方AI主机</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"feature-cards\">\r\n\t\t\t\t\t<view class=\"feature-card\" v-for=\"(feature, index) in features\" :key=\"index\">\r\n\t\t\t\t\t\t<image :src=\"feature.icon\" mode=\"aspectFit\" class=\"feature-icon\" />\r\n\t\t\t\t\t\t<text class=\"feature-title\">{{ feature.title }}</text>\r\n\t\t\t\t\t\t<text class=\"feature-desc\">{{ feature.description }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<!-- <view class=\"section-title\">\r\n\t\t\t\t\t<uni-icons type=\"medal\" size=\"24\" color=\"#4169E1\" />\r\n\t\t\t\t\t<text class=\"title-text\">系统优势</text>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"advantage-list\">\r\n\t\t\t\t\t<view class=\"advantage-item\" v-for=\"(advantage, index) in advantages\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"advantage-icon-wrapper\">\r\n\t\t\t\t\t\t\t<uni-icons :type=\"advantage.icon\" size=\"24\" color=\"#4169E1\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"advantage-content\">\r\n\t\t\t\t\t\t\t<text class=\"advantage-title\">{{ advantage.title }}</text>\r\n\t\t\t\t\t\t\t<text class=\"advantage-desc\">{{ advantage.description }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<!-- <view class=\"section-title\">\r\n\t\t\t\t\t<uni-icons type=\"gear\" size=\"24\" color=\"#4169E1\" />\r\n\t\t\t\t\t<text class=\"title-text\">应用场景</text>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"scenario-grid\">\r\n\t\t\t\t\t<view class=\"scenario-card\" v-for=\"(scenario, index) in scenarios\" :key=\"index\">\r\n\t\t\t\t\t\t<image :src=\"scenario.image\" mode=\"aspectFill\" class=\"scenario-image\" />\r\n\t\t\t\t\t\t<view class=\"scenario-info\">\r\n\t\t\t\t\t\t\t<text class=\"scenario-title\">{{ scenario.title }}</text>\r\n\t\t\t\t\t\t\t<text class=\"scenario-desc\">{{ scenario.description }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbanners: [{\r\n\t\t\t\t\t\timage: 'https://ai-public.mastergo.com/ai/img_res/1747114705cd89a0b636d09b6117fc5f.jpg'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\timage: 'https://ai-public.mastergo.com/ai/img_res/21749e007ca9c553e43e57e2ca755cbb.jpg'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\timage: 'https://ai-public.mastergo.com/ai/img_res/ea68981430e1307646a14e900ce6d3e6.jpg'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tfeatures: [{\r\n\t\t\t\t\t\tname: '豆包',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\r\n\t\t\t\t\t\ticon: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\r\n\t\t\t\t\t\ttitle: '豆包',\r\n\t\t\t\t\t\tdescription: '字节跳动开发的AI助手，擅长深度思考和逻辑推理',\r\n\t\t\t\t\t\ttype: 'doubao'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: 'DeepSeek',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/Deepseek.png',\r\n\t\t\t\t\t\ticon: 'https://u3w.com/chatfile/Deepseek.png',\r\n\t\t\t\t\t\ttitle: 'DeepSeek',\r\n\t\t\t\t\t\tdescription: 'DeepSeek原生AI助手，擅长深度思考和联网搜索',\r\n\t\t\t\t\t\ttype: 'doubao'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: 'MiniMax Chat',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/MiniMaxChat.png',\r\n\t\t\t\t\t\ticon: 'https://u3w.com/chatfile/MiniMaxChat.png',\r\n\t\t\t\t\t\ttitle: 'MiniMax Chat',\r\n\t\t\t\t\t\tdescription: 'MiniMax ChatAI助手，提供专业、严谨的解答。',\r\n\t\t\t\t\t\ttype: 'mini'\r\n\t\t\t\t\t},\r\n          {\r\n            name: '通义千问',\r\n            avatar: 'https://u3w.com/chatfile/TongYi.png',\r\n            icon: 'https://u3w.com/chatfile/TongYi.png',\r\n            title: '通义千问',\r\n            description: '通情、达义，你的全能AI助手。超级助手，答你所问。生活办公，效率翻倍。个性智能体，丰富角色趣味互动。',\r\n            type: 'tongyi'\r\n          },\r\n\t\t\t\t],\r\n\t\t\t\tadvantages: [\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \ticon: 'star-filled',\r\n\t\t\t\t\t// \ttitle: '多模型协同',\r\n\t\t\t\t\t// \tdescription: '整合多个AI模型，实现优势互补，提供更全面的解决方案'\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \ticon: 'refresh',\r\n\t\t\t\t\t// \ttitle: '实时响应',\r\n\t\t\t\t\t// \tdescription: '快速处理请求，毫秒级响应，提供流畅的用户体验'\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \ticon: 'shield',\r\n\t\t\t\t\t// \ttitle: '安全可靠',\r\n\t\t\t\t\t// \tdescription: '采用先进的安全防护措施，保障数据和隐私安全'\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \ticon: 'staff',\r\n\t\t\t\t\t// \ttitle: '专业支持',\r\n\t\t\t\t\t// \tdescription: '24小时专业团队支持，解决您的使用疑难'\r\n\t\t\t\t\t// }\r\n\t\t\t\t],\r\n\t\t\t\tscenarios: [\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \timage: 'https://ai-public.mastergo.com/ai/img_res/3460a8269aff64d63683572370ab0b59.jpg',\r\n\t\t\t\t\t// \ttitle: '企业管理',\r\n\t\t\t\t\t// \tdescription: '智能化办公，提升管理效率'\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \timage: 'https://ai-public.mastergo.com/ai/img_res/038be95571253851dbc71264b8a0dc96.jpg',\r\n\t\t\t\t\t// \ttitle: '金融分析',\r\n\t\t\t\t\t// \tdescription: '精准的市场分析和预测'\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \timage: 'https://ai-public.mastergo.com/ai/img_res/0fa10d1093ca0600699e733d035a67ba.jpg',\r\n\t\t\t\t\t// \ttitle: '数据研究',\r\n\t\t\t\t\t// \tdescription: '深度挖掘数据价值'\r\n\t\t\t\t\t// }\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\theight: 100%;\r\n\t\tbackground-color: #F5F7FA;\r\n\t}\r\n\r\n\t.swiper-container {\r\n\t\tflex-shrink: 0;\r\n\t\theight: 400rpx;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.swiper {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.swiper-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.content {\r\n\t\tflex: 1;\r\n\t\toverflow: auto;\r\n\t}\r\n\r\n\t.section {\r\n\t\tmargin: 30rpx 20rpx;\r\n\t\tbackground: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 50rpx 40rpx;\r\n\t\tbox-shadow:\r\n\t\t\t0 8rpx 32rpx rgba(0, 0, 0, 0.04),\r\n\t\t\t0 2rpx 16rpx rgba(0, 0, 0, 0.02);\r\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.8);\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.section::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\theight: 3rpx;\r\n\t\tbackground: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\r\n\t\topacity: 0.6;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 50rpx;\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t}\r\n\r\n\t.title-text {\r\n\t\tmargin-left: 20rpx;\r\n\t\tfont-size: 22px;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #2d3748;\r\n\t\tletter-spacing: 0.8px;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.title-text::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\tbottom: -8rpx;\r\n\t\tleft: 0;\r\n\t\twidth: 60rpx;\r\n\t\theight: 4rpx;\r\n\t\tbackground: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\r\n\t\tborder-radius: 2rpx;\r\n\t\topacity: 0.7;\r\n\t}\r\n\r\n\t.feature-cards {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));\r\n\t\tgap: 30rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.feature-card {\r\n\t\tbackground: linear-gradient(145deg, #ffffff 0%, #f8faff 100%);\r\n\t\tpadding: 40rpx 30rpx 35rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow:\r\n\t\t\t0 8rpx 24rpx rgba(0, 0, 0, 0.06),\r\n\t\t\t0 2rpx 8rpx rgba(0, 0, 0, 0.04);\r\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.8);\r\n\t\ttransition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\r\n\t}\r\n\r\n\t.feature-card::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\theight: 6rpx;\r\n\t\tbackground: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\r\n\t\topacity: 0.8;\r\n\t}\r\n\r\n\t.feature-card::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: -50%;\r\n\t\tleft: -50%;\r\n\t\twidth: 200%;\r\n\t\theight: 200%;\r\n\t\tbackground: radial-gradient(circle, rgba(102, 126, 234, 0.03) 0%, transparent 70%);\r\n\t\topacity: 0;\r\n\t\ttransition: opacity 0.4s ease;\r\n\t}\r\n\r\n\t.feature-card:hover {\r\n\t\ttransform: translateY(-8rpx) scale(1.01);\r\n\t\tbox-shadow:\r\n\t\t\t0 16rpx 32rpx rgba(102, 126, 234, 0.12),\r\n\t\t\t0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n\t\tborder-color: rgba(102, 126, 234, 0.15);\r\n\t}\r\n\r\n\t.feature-card:hover::after {\r\n\t\topacity: 1;\r\n\t}\r\n\r\n\t.feature-icon {\r\n\t\twidth: 72rpx;\r\n\t\theight: 72rpx;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\r\n\t\ttransition: all 0.3s ease;\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t}\r\n\r\n\t.feature-card:hover .feature-icon {\r\n\t\ttransform: translateY(-4rpx) scale(1.05);\r\n\t\tbox-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.15);\r\n\t}\r\n\r\n\t.feature-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #2d3748;\r\n\t\tmargin-bottom: 12rpx;\r\n\t\tletter-spacing: 0.3px;\r\n\t\ttext-align: center;\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t\tline-height: 1.3;\r\n\t}\r\n\r\n\t.feature-desc {\r\n\t\tfont-size: 13px;\r\n\t\tcolor: #64748b;\r\n\t\ttext-align: center;\r\n\t\tline-height: 1.6;\r\n\t\tmax-width: 220rpx;\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t\topacity: 0.85;\r\n\t}\r\n\r\n\t.advantage-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t.advantage-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #F8F9FF;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.advantage-icon-wrapper {\r\n\t\tflex-shrink: 0;\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground-color: #E6EFFF;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.advantage-content {\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.advantage-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.advantage-desc {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.scenario-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t.scenario-card {\r\n\t\tflex: 1;\r\n\t\tmin-width: 280rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.scenario-image {\r\n\t\twidth: 100%;\r\n\t\theight: 200rpx;\r\n\t}\r\n\r\n\t.scenario-info {\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t.scenario-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.scenario-desc {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754063445406\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}