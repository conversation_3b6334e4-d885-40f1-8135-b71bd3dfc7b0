@charset "UTF-8";
/**
 * uni-app内置的常用样式变量
 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-easyinput {
  width: 100%;
  flex: 1;
  position: relative;
  text-align: left;
  color: #333;
  font-size: 14px;
}
.uni-easyinput__content {
  flex: 1;
  width: 100%;
  display: flex;
  box-sizing: border-box;
  flex-direction: row;
  align-items: center;
  border-color: #fff;
  transition-property: border-color;
  transition-duration: 0.3s;
}
.uni-easyinput__content-input {
  width: auto;
  position: relative;
  overflow: hidden;
  flex: 1;
  line-height: 1;
  font-size: 14px;
  height: 35px;
}
.uni-easyinput__placeholder-class {
  color: #999;
  font-size: 12px;
}
.is-textarea {
  align-items: flex-start;
}
.is-textarea-icon {
  margin-top: 5px;
}
.uni-easyinput__content-textarea {
  position: relative;
  overflow: hidden;
  flex: 1;
  line-height: 1.5;
  font-size: 14px;
  margin: 6px;
  margin-left: 0;
  height: 80px;
  min-height: 80px;
  min-height: 80px;
  width: auto;
}
.input-padding {
  padding-left: 10px;
}
.content-clear-icon {
  padding: 0 5px;
}
.label-icon {
  margin-right: 5px;
  margin-top: -1px;
}
.is-input-border {
  display: flex;
  box-sizing: border-box;
  flex-direction: row;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
.uni-error-message {
  position: absolute;
  bottom: -17px;
  left: 0;
  line-height: 12px;
  color: #e43d33;
  font-size: 12px;
  text-align: left;
}
.uni-error-msg--boeder {
  position: relative;
  bottom: 0;
  line-height: 22px;
}
.is-input-error-border {
  border-color: #e43d33;
}
.is-input-error-border .uni-easyinput__placeholder-class {
  color: #f29e99;
}
.uni-easyinput--border {
  margin-bottom: 0;
  padding: 10px 15px;
  border-top: 1px #eee solid;
}
.uni-easyinput-error {
  padding-bottom: 0;
}
.is-first-border {
  border: none;
}
.is-disabled {
  background-color: #f7f6f6;
  color: #d5d5d5;
}
.is-disabled .uni-easyinput__placeholder-class {
  color: #d5d5d5;
  font-size: 12px;
}
