{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/work/index.vue?96cc", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/work/index.vue?13de", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/work/index.vue?3932", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/work/index.vue?a516", "uni-app:///pages/work/index.vue", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/work/index.vue?754a", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/work/index.vue?5574"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "userId", "corpId", "chatId", "expandedHistoryItems", "userInfoReq", "userPrompt", "taskId", "roles", "toneChatId", "ybDsChatId", "dbChatId", "tyChatId", "isNewChat", "jsonRpcReqest", "jsonrpc", "id", "method", "params", "sectionExpanded", "aiConfig", "promptInput", "taskStatus", "aiList", "avatar", "capabilities", "label", "value", "selectedCapabilities", "enabled", "status", "progressLogs", "isExpanded", "selectedCapability", "taskStarted", "enabledAIs", "screenshots", "autoPlay", "results", "activeResultIndex", "chatHistory", "selectedResults", "scorePrompt", "collectNum", "layoutPrompt", "selectedMedia", "tthArticleEditVisible", "tthArticleTitle", "tthArticleContent", "tthFlowVisible", "tthFlowLogs", "tthFlowImages", "tthScoreContent", "socketTask", "reconnectTimer", "heartbeatTimer", "reconnectCount", "maxReconnectCount", "isConnecting", "scrollIntoView", "historyDrawerVisible", "scoreModalVisible", "layoutModalVisible", "currentLayoutResult", "aiLoginStatus", "do<PERSON>o", "deepseek", "tongyi", "mini", "accounts", "isLoading", "computed", "canSend", "canScore", "console", "currentResult", "groupedHistory", "chatGroups", "Object", "chatGroup", "groups", "parentItem", "isParent", "children", "child", "tthArticleContentLength", "isTthArticleContentExceeded", "watch", "handler", "textarea", "immediate", "onLoad", "uni", "title", "content", "showCancel", "confirmText", "success", "url", "onUnload", "methods", "handleTextareaFocus", "bubbles", "initUserInfo", "generateUUID", "toggleSection", "toggleAI", "ai", "toggleCapability", "selectSingleCapability", "sendPrompt", "icon", "initWebSocket", "fail", "duration", "handleReconnect", "startHeartbeat", "type", "timestamp", "stopHeartbeat", "clearInterval", "sendWebSocketMessage", "message", "closeWebSocket", "clearTimeout", "handleWebSocketMessage", "targetAI", "isCompleted", "wkpfAI", "aiName", "shareUrl", "shareImgUrl", "znpbAI", "zhihuAI", "baijiahaoAI", "tthZnpbAI", "handleAiStatusMessage", "handleAIResult", "getStatusText", "getStatusIconClass", "getStatusEmoji", "toggleTaskExpansion", "toggleAutoPlay", "previewImage", "current", "urls", "switchResultTab", "renderMarkdown", "isImageFile", "isPdfFile", "copyResult", "exportResult", "openShareUrl", "copyPdfUrl", "openPdfFile", "filePath", "showHistoryDrawer", "closeHistoryDrawer", "loadChatHistory", "res", "loadHistoryItem", "loadLastChat", "lastChat", "saveHistory", "historyData", "maxChatId", "getHistoryDate", "date", "year", "month", "day", "hour", "minute", "second", "parseInt", "yesterday", "formatHistoryTime", "toggleHistoryExpansion", "showScoreModal", "closeScoreModal", "showLayoutModal", "selectMedia", "loadMediaPrompt", "platformId", "getDefaultPrompt", "closeLayoutModal", "handleLayout", "createZhihuDeliveryTask", "createBaijiahaoDeliveryTask", "createToutiaoLayoutTask", "createWechatLayoutTask", "addOrUpdateTaskAI", "handlePushToWechat", "contentText", "num", "toggleResultSelection", "handleScore", "filter", "map", "join", "createNewChat", "checkAiLoginStatus", "setTimeout", "sendAiStatusCheck", "getPlatformIcon", "yuanbao", "agent", "getPlatformName", "refreshAiStatus", "isAiLoginEnabled", "isAiInLoading", "disableAIsByLoginStatus", "updateAiEnabledStatus", "showTthArticleEditModal", "closeTthArticleEditModal", "confirmTTHPublish", "closeTthFlowDialog", "htmlToText", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtKA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACsbppB;AAGA;AAGA;AAGA;AACA;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAL;QACAC;QACAK;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAxB;QACAyB;QACAC;UACAC;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAjC;QACAyB;QACAC;UACAC;UACAC;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAjC;QACAyB;QACAC,eACA;UACAC;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAM;QACAJ;QACAC;QACAC;QACAC;MACA,GACA;QACAjC;QACAyB;QACAC,eACA;UAAAC;UAAAC;QAAA,GACA;UAAAD;UAAAC;QAAA,EACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MAEA;MACAX;MACAa;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MAAA;;MAEA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAJ;QACAC;QACAC;QACAC;MACA;MACAE;QACAL;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAG;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;MAEA;IACA;IAEAC;MACA;MACA;MACAC;MACAA;MACAA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MAAA;MACA;MACA;;MAEA;MACA;QACA;UACAC;QACA;QACAA;MACA;;MAEA;MACAC;QACA;QACAC;UAAA;QAAA;;QAEA;QACA;QACA;QAEA;UACAC;QACA;;QAEA;QACAA,kDACAC;UACAC;UACAlD;UACAmD;YAAA,uCACAC;cACAF;YAAA;UAAA,CACA;QAAA,GACA;MACA;MAEA;IACA;IAEA;IACAG;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAvC;MACAwC;QAAA;QACA;QACA;UACA;UACA;YACAC;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;;IAEA;IACA;MACAjB;MACAkB;QACAC;QACAC;QACAC;QACAC;QACAC;UACAL;YACAM;UACA;QACA;MACA;MACA;IACA;IAEA;IACA;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;UACAZ;UACA;UACAA;YAAAa;UAAA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MAEA;;MAEA;MACA;MACA;MAEA7B;QACAzE;QACAC;MACA;IACA;IAEA;IACAsG;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAd;UACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MACAW;IACA;IAEA;IACAC;MACA;MACA;QACAhB;UACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;MAEA;MACA;QACAW;MACA;QACAA;MACA;IACA;IACA;IACAE;MACA;MAEA;QACAF;MACA;QACAA;MACA;IACA;IAEA;IACAG;MAAA;MACA;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;QAAA;MAAA;;MAEA;MACA;QACAH;MACA;;MAEA;MACA;QACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;QACA;QACA;UACA;UACA;YACA;UACA;QACA;QACA;UACA;YACA;YACA;cACA;YACA;YACA;cACA;YACA;UACA;QACA;QACA;UACA;UACA;YACA;UACA;YACA;UACA;QACA;MACA;MAEAjC;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MAEAkB;QACAC;QACAkB;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACAtC;QACAkB;UACAC;UACAC;UACAC;UACAC;UACAC;YACAL;cACAM;YACA;UACA;QACA;QACA;MACA;MAEA;QACAxB;QACA;MACA;MAEA;;MAEA;MACA;MACA;MACAA;MAEA;QACAwB;QACAD;UACAvB;QACA;QACAuC;UACAvC;UACA;UACA;QACA;MACA;MAEA;QACAA;QACA;QACA;;QAEAkB;UACAC;UACAkB;UACAG;QACA;;QAEA;QACA;MACA;MAEA;QACA;MACA;MAEA;QACAxC;QACA;QACAkB;UACAC;UACAkB;QACA;QACA;MACA;MAEA;QACArC;QACA;QACA;;QAEAkB;UACAC;UACAkB;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACAI;MAAA;MACA;QACAzC;QACAkB;UACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;MACA;;MAEAtB;MAEA;QACAA;QACA;MACA;IACA;IAEA;IACA0C;MAAA;MACA;;MAEA;QACA;UACA;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACA;MACA;IACA;IAEAC;MACA;QACA;UACAzH;QACA;MACA;QACA0E;MACA;IACA;IAEA;IACAgD;MACA;QACA;UACA9B;YACAC;YACAkB;YACAG;UACA;QACA;MACA;IACA;IAEAS;MACA;MACA;QACAC;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;;QAEA;QACA;UACA;YAAA;UAAA;UACA;YACA;YACAC;cACAhC;cACAwB;cACAS;YACA;UACA;UACA;QACA;;QAEA;QACA;UACA;UACA;UACA;QACA;;QAEA;QACA;UACA;YAAA;UAAA;UACA;YACAC;YACA;cACAA;YACA;YACA;YACA;cACAC;cACAnC;cACAoC;cACAC;cACAb;YACA;YACA;;YAEA;YACA;YACA;YACA;;YAEA;YACA;UACA;UACA;QACA;;QAEA;QACA;UACA5C;UACAA;UAEA;YAAA;UAAA;UACA;YACA0D;YACA;cACAA;YACA;;YAEA;YACA;;YAEA;YACA;UACA;UACA;QACA;QACA;QACA;UACA1D;UACA;YAAA;UAAA;UACA;YACA;YACA;cAAA;YAAA;YACA;cACA;cACA2D;gBACAvC;gBACAwB;gBACAS;gBACAV;cACA;;cAEA;cACA;YACA;UACA;UACA;QACA;;QAEA;QACA;UACA3C;UACA;YAAA;UAAA;UACA;YACA2D;;YAEA;YACA;cACAA;YACA;;YAEA;YACAA;cACAvC;cACAwB;cACAS;cACAV;YACA;;YAEA;YACA;;YAEA;YACAzB;cACAC;cACAkB;YACA;;YAEA;YACA;UACA;UACA;QACA;;QAEA;QACA;UACArC;UACA;YAAA;UAAA;UACA;YACA;YACA;cAAA;YAAA;YACA;cACA;cACA4D;gBACAxC;gBACAwB;gBACAS;gBACAV;cACA;;cAEA;cACA;YACA;UACA;UACA;QACA;;QAEA;QACA;UACA3C;UACA;YAAA;UAAA;UACA;YACA4D;;YAEA;YACA;cACAA;YACA;;YAEA;YACAA;cACAxC;cACAwB;cACAS;cACAV;YACA;;YAEA;YACA;;YAEA;YACAzB;cACAC;cACAkB;YACA;;YAEA;YACA;UACA;UACA;QACA;;QAEA;QACA;UACA;UACA;YAAA;UAAA;UACA;YACAwB;YACA;cACAA;YACA;UACA;UACA;UACA;UACA;;UAEA;UACA;YACA;YACA;YACA;cACA9C;cACA;cACAA;gBAAAa;cAAA;YACA;UACA;UAEA;YACA;UACA;UACAV;YAAAC;YAAAkB;UAAA;UACA;QACA;;QAEA;QACA;UACA;YACA;cACAjB;cACAwB;cACAD;YACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACAzB;cAAAC;cAAAkB;YAAA;YACA;UACA;UACA;YACAnB;cAAAC;cAAAkB;YAAA;YACA;YACA;UACA;UACA;QACA;;QAIA;QACA;;QAEA;QACA;MAEA;QACArC;MACA;IACA;IAEA8D;MACA;MACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;QACA;QACA;MACA;MACA;MAAA,KACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;QACA;QACA;MACA;MACA;MAAA,KACA;QACA9D;QACA;QACA;UACA;UACA;UACAA;;UAEA;UACA;YAAA;UAAA;QAEA;UACA;UACA;UACAA;;UAEA;UACA;YAAA;UAAA;QAEA;QACA;QACA;MACA,OACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;QACA;QACA;MACA;IACA;IAEA+D;MACA;;MAEA;MACA;QACA;UACA/D;UACAoD;YAAA;UAAA;UACA;QACA;UACApD;UACAoD;YAAA;UAAA;UACA;UACA;YACAA;cACA/H;cACAyB;cACAC;gBACAC;gBACAC;cACA,GACA;gBACAD;gBACAC;cACA;cACAC;cACAC;cACAC;cACAC;gBACA+D;gBACAwB;gBACAS;cACA;cACA/F;YACA;YACA;UACA;UACA;QACA;UACA0C;UACAoD;YAAA;UAAA;UACA;QACA;UACApD;UACAoD;YAAA;UAAA;UACA;MAAA;MAGA;QACA;QACAA;;QAEA;QACA;UACAA;QACA;;QAEA;QACA;UAAA;QAAA;QACA;UACA;YACAG;YACAnC;YACAoC;YACAC;YACAb;UACA;UACA;QACA;UACA;UACA;YACAW;YACAnC;YACAoC;YACAC;YACAb;UACA;UACA;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACAoB;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACAlC;IACA;IAEA;IACAmC;MACA;IACA;IAEA;IACAC;MACAnD;QACAoD;QACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEAC;MACA;QACA;QACA;UACA;UACA;YACA;UACA;;UACA;UACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACA1D;QACA5F;QACAiG;UACAL;YACAC;YACAkB;UACA;QACA;MACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEAwC;MACA;MACA;IACA;IAEAC;MACA5D;QACA5F;QACAiG;UACAL;YACAC;YACAkB;UACA;QACA;QACAE;UACArB;YACAC;YACAkB;UACA;QACA;MACA;IACA;IAEA;IACA0C;MACA7D;QACA5F;QACAiG;UACAL;YACAC;YACAkB;UACA;QACA;QACAE;UACArB;YACAC;YACAkB;UACA;QACA;MACA;IACA;IAEA;IACA2C;MACA9D;QACAC;MACA;;MAEA;MACAD;QACAM;QACAD;UACAL;UACA;YACA;YACAA;cACA+D;cACA1D;gBACAL;kBACAC;kBACAkB;gBACA;cACA;cACAE;gBACA;gBACArB;kBACAC;kBACAC;kBACAC;kBACAE;oBACAL;sBACA5F;oBACA;kBACA;gBACA;cACA;YACA;UACA;YACA4F;cACAC;cACAkB;YACA;UACA;QACA;QACAE;UACArB;UACA;UACAA;YACAC;YACAC;YACAC;YACAE;cACAL;gBACA5F;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACA4J;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEArF;gBACAkB;kBACAC;kBACAkB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAiD;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;;QAEA;QACA;QACA;QACA;QACA;QAEA;QACApE;UACAC;UACAkB;QACA;MACA;QACArC;QACAkB;UACAC;UACAkB;QACA;MACA;IACA;IAEA;IACAkD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAF;gBACA;kBACA;kBACAG;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAxF;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAyF;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACA7I;kBACAF;kBACAc;kBACAC;kBACAE;kBACAnC;kBACAM;kBACAC;kBACAC;kBACAC;kBACAyJ;gBACA;gBAAA;gBAAA;gBAAA,OAGA;kBACApK;kBACAK;kBACAN;kBACAG;kBACAM;kBACAC;kBACAC;kBACAC;kBACAyJ;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA3F;gBACAkB;kBACAC;kBACAkB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAuD;MACA;QACA5F;QAEA;UACA;QACA;QAEA;QAEA;UACA6F;QACA;UACA;UACA;UACA;YACA;cAAAC;cAAAC;cAAAC;cAAAC;cAAAC;cAAAC;YACAN,gBACAO,gBACAA,qBACAA,eACAA,gBACAA,kBACAA,iBACA;UACA;YACA;YACA;YACAP;YAEA;cACAA;YACA;UACA;QACA;UACAA;QACA;QAEA7F;QAEA;UACA;QACA;QAEA;QACA;QACAqG;QAEA;UACA;QACA;UACA;QACA;UACA;QACA;MACA;QACArG;QACA;MACA;IACA;IAEA;IACAsG;MACA;QACAtG;QAEA;QAEA;UACA;QACA;;QAEA;QACA;UACA6F;QACA;UACA;UACA;YACAA;UACA;UACA;UAAA,KACA;YACA;YACA;cACA;gBAAAC;gBAAAC;gBAAAC;gBAAAC;gBAAAC;gBAAAC;cACA;cACAN,gBACAO,gBACAA,qBACAA,eACAA,iBACAA,mBACAA,iBACA;YACA;cACA;cACA;cACAP;cAEA;gBACAA;cACA;YACA;UACA;QACA;UACAA;QACA;UACAA;QACA;QAEA7F;QAEA;UACA;QACA;;QAEA;QACA;QACA;QAEA;QAEAA;QACA;MAEA;QACAA;QACA;MACA;IACA;IAEA;IACAuG;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MACA;QACAxF;UACAC;UACAkB;QACA;QACA;MACA;MACArC;MACA;MACA;QACAuD;QACAnC;QACAoC;QACAC;QACAb;MACA;;MAEA;MACA;MACA;MACA;MACA;IACA;IACA;IACA+D;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAGA;kBACAC;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAxB;gBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEArF;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8G;MACA;QACA;MAWA;QACA;MAUA;QACA;MAaA;QACA;MAUA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;MAEA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;QACA5K;QACAC;QACAC;QACAC;UACAX;UACAN;UACAC;UACAI;UACA2H;UACAnC;QACA;MACA;MAEApB;MACA;;MAEA;MACA;QACA3E;QACAyB;QACAC;QACAG;QACAC;QACAC;QACAC,eACA;UACA+D;UACAwB;UACAS;UACAV;QACA,EACA;QACArF;MACA;MAEA;MAEA4D;QACAC;QACAkB;MACA;IACA;IAEA;IACA6E;MACA;MACA;;MAEA;MACA;QACA7K;QACAC;QACAC;QACAC;UACAX;UACAN;UACAC;UACAI;UACA2H;UACAnC;QACA;MACA;MAEApB;MACA;;MAEA;MACA;QACA3E;QACAyB;QACAC;QACAG;QACAC;QACAC;QACAC,eACA;UACA+D;UACAwB;UACAS;UACAV;QACA,EACA;QACArF;MACA;MAEA;MAEA4D;QACAC;QACAkB;MACA;IACA;IAGA;IACA8E;MACA;MACA;;MAEA;MACA;QACA9K;QACAC;QACAC;QACAC;UACAX;UACAN;UACAC;UACAI;UACAE;QACA;MACA;MAEAkE;MACA;;MAEA;MACA;QACA3E;QACAyB;QACAC;QACAG;QACAC;QACAC;QACAC,eACA;UACA+D;UACAwB;UACAS;UACAV;QACA,EACA;QACArF;MACA;MAEA;MAEA4D;QACAC;QACAkB;MACA;IACA;IAEA;IACA+E;MACA;MACA;;MAEA;MACA;QACA/K;QACAC;QACAC;QACAC;UACAX;UACAN;UACAC;UACAI;UACAE;QACA;MACA;;MAEA;MACAkE;MACA;MACA;;MAEA;MACA;QACA3E;QACAyB;QACAC;QACAG;QACAC;QACAC;QACAC,eACA;UACA+D;UACAwB;UACAS;UACAV;QACA,EACA;QACArF;MACA;MACA;MAEA4D;QACAC;QACAkB;MACA;IACA;IAEA;IACAgF;MACA;QAAA;MAAA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IAGA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAtH;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAA;gBACAkB;kBACAC;kBACAkB;gBACA;gBAAA;cAAA;gBAIAnB;kBACAC;gBACA;;gBAEA;gBACA;gBAEA3E;kBACA+K;kBACAhM;kBACAiI;kBACAD;kBACAiE;gBACA;gBAEAxH;gBAAA;gBAAA,OAEA;cAAA;gBAAAqF;gBAEAnE;gBAEA;kBACAA;oBACAC;oBACAkB;kBACA;gBACA;kBACAnB;oBACAC;oBACAkB;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnB;gBACAlB;gBACAkB;kBACAC;kBACAkB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAoF;MACA;MACAzH;MACAA;MACA;MACAA;MACAA;IACA;IAEA0H;MAAA;MACA;;MAEA;MACA,oCACAC;QAAA;MAAA,GACAC;QACA;QACA;QACA;MACA,GACAC;;MAEA;MACA;;MAEA;MACA;QACAxL;QACAC;QACAC;QACAC;UACAX;UACAN;UACAC;UACAI;UACAE;QACA;MACA;;MAEA;MACAkE;MACA;MACA;;MAEA;MACA;QACA3E;QACAyB;QACAC;QACAG;QACAC;QACAC;QACAC,eACA;UACA+D;UACAwB;UACAS;UACAV;QACA,EACA;QACArF;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA4D;QACAC;QACAkB;MACA;IACA;IAEA;IACAyF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAlM;QACAL;QACAC;QACAK;QACAC;QACAC;QACAC;QACAC;QACAC;QACAyJ;QACAxJ;MACA;MACA;MACA;QACAd;QACAyB;QACAC;UACAC;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAjC;QACAyB;QACAC;UACAC;UACAC;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAjC;QACAyB;QACAC,eACA;UAAAC;UAAAC;QAAA,GACA;UAAAD;UAAAC;QAAA,EACA;QACAM;QACAJ;QACAC;QACAC;QACAC;MACA,GACA;QACAjC;QACAyB,QACA;QACAC,eACA;UAAAC;UAAAC;QAAA,GACA;UAAAD;UAAAC;QAAA,EACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACA;;MAEA;MACA;MACA;MACA;MAEA4D;QACAC;QACAkB;MACA;IACA;IAEA;IACA0F;MAAA;MACA;MACAC;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;QACAtF;QACApH;QACAC;MACA;;MAEA;MACA;QACAmH;QACApH;QACAC;MACA;;MAEA;MACA;QACAmH;QACApH;QACAC;MACA;;MAEA;MACA;QACAmH;QACApH;QACAC;MACA;IACA;IAEA0M;MACA;QACAC;QACA5I;QACA6I;QACA3I;MACA;MACA;IACA;IAEA4I;MACA;QACAF;QACA5I;QACA6I;QACA3I;MACA;MACA;IACA;IAMA6I;MAAA;MACA;MACA;QACA/I;QACAC;QACAC;QACAC;MACA;;MAEA;MACA;QACAH;QACAC;QACAE;QACAD;MACA;;MAEA;MACA;QACAF;QACAC;QACAC;QACAC;MACA;;MAEA;MACAwB;QACAC;QACAkB;QACAG;MACA;;MAEA;MACA;MACAwF;QACA;QACA;QACAA;UACA;QACA;MACA;IACA;IAEA;IACAO;MACA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;UACA;QACA;UACA;QAAA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;MACAzI;IACA;IAEA;IACA0I;MACA;MACA1I;IACA;IAEA;IACA;IACA2I;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;QACA3H;UAAAC;UAAAkB;QAAA;QACA;MACA;MACA;QACAhG;QACAC;QACAC;QACAC;UACAX;UACAN;UACAC;UACAM;UACAqF;UACAC;UACAuB;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAzB;QAAAC;QAAAkB;MAAA;IACA;IAIA;IACAyG;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;QACA;QAEA;QAEA;UACAnD;QACA;UACA;UACA;YACAA;UACA;UACA;UAAA,KACA;YACA;YACA;cACA;gBAAAC;gBAAAC;gBAAAC;gBAAAC;gBAAAC;gBAAAC;cACAN,gBACAO,gBACAA,qBACAA,eACAA,kBACAA,oBACAA,kBACA;YACA;cACA;cACA;cACAP;cAEA;gBACAA;cACA;YACA;UACA;QACA;UACAA;QACA;UACAA;QACA;QAEA;UACA;QACA;;QAEA;QACA;QACA;QACA;QAEA;QAEA;MAEA;QACA7F;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjzFA;AAAA;AAAA;AAAA;AAAu7B,CAAgB,43BAAG,EAAC,C;;;;;;;;;;;ACA38B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/work/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/work/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=51b5538d&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=51b5538d&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"51b5538d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/work/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=51b5538d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l2 = _vm.sectionExpanded.aiConfig\n    ? _vm.__map(_vm.aiList, function (ai, index) {\n        var $orig = _vm.__get_orig(ai)\n        var m0 = ai.enabled && _vm.isAiLoginEnabled(ai)\n        var m1 = _vm.isAiLoginEnabled(ai)\n        var m2 = _vm.isAiLoginEnabled(ai)\n        var m3 = !_vm.isAiLoginEnabled(ai) && !_vm.isAiInLoading(ai)\n        var m4 = _vm.isAiInLoading(ai)\n        var m5 = ai.enabled && _vm.isAiLoginEnabled(ai)\n        var m6 = !_vm.isAiLoginEnabled(ai) || _vm.isAiInLoading(ai)\n        var g0 = ai.capabilities.length\n        var l0 =\n          g0 > 0 && ai.name === \"通义千问\"\n            ? _vm.__map(ai.capabilities, function (capability, capIndex) {\n                var $orig = _vm.__get_orig(capability)\n                var m7 = !ai.enabled || !_vm.isAiLoginEnabled(ai)\n                return {\n                  $orig: $orig,\n                  m7: m7,\n                }\n              })\n            : null\n        var l1 =\n          g0 > 0 && !(ai.name === \"通义千问\")\n            ? _vm.__map(ai.capabilities, function (capability, capIndex) {\n                var $orig = _vm.__get_orig(capability)\n                var g1 = ai.selectedCapabilities.includes(capability.value)\n                var m8 = !ai.enabled || !_vm.isAiLoginEnabled(ai)\n                return {\n                  $orig: $orig,\n                  g1: g1,\n                  m8: m8,\n                }\n              })\n            : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n          m3: m3,\n          m4: m4,\n          m5: m5,\n          m6: m6,\n          g0: g0,\n          l0: l0,\n          l1: l1,\n        }\n      })\n    : null\n  var g2 = _vm.sectionExpanded.promptInput ? _vm.promptInput.length : null\n  var l4 =\n    _vm.taskStarted && _vm.sectionExpanded.taskStatus\n      ? _vm.__map(_vm.enabledAIs, function (ai, index) {\n          var $orig = _vm.__get_orig(ai)\n          var m9 = _vm.getStatusText(ai.status)\n          var m10 = _vm.getStatusIconClass(ai.status)\n          var m11 = _vm.getStatusEmoji(ai.status)\n          var g3 = ai.isExpanded && ai.progressLogs.length > 0\n          var l3 = g3\n            ? _vm.__map(ai.progressLogs, function (log, logIndex) {\n                var $orig = _vm.__get_orig(log)\n                var m12 = _vm.formatTime(log.timestamp)\n                return {\n                  $orig: $orig,\n                  m12: m12,\n                }\n              })\n            : null\n          return {\n            $orig: $orig,\n            m9: m9,\n            m10: m10,\n            m11: m11,\n            g3: g3,\n            l3: l3,\n          }\n        })\n      : null\n  var g4 = _vm.results.length\n  var m13 =\n    g4 > 0 && _vm.currentResult\n      ? _vm.currentResult.shareImgUrl &&\n        _vm.isImageFile(_vm.currentResult.shareImgUrl)\n      : null\n  var m14 =\n    g4 > 0 && _vm.currentResult && !m13\n      ? _vm.currentResult.shareImgUrl &&\n        _vm.isPdfFile(_vm.currentResult.shareImgUrl)\n      : null\n  var m15 =\n    g4 > 0 &&\n    _vm.currentResult &&\n    !m13 &&\n    !m14 &&\n    !(_vm.currentResult.aiName === \"DeepSeek\")\n      ? _vm.renderMarkdown(_vm.currentResult.content)\n      : null\n  var l6 = _vm.historyDrawerVisible\n    ? _vm.__map(_vm.groupedHistory, function (group, date) {\n        var $orig = _vm.__get_orig(group)\n        var l5 = _vm.__map(group, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m16 = _vm.formatHistoryTime(item.createTime)\n          return {\n            $orig: $orig,\n            m16: m16,\n          }\n        })\n        return {\n          $orig: $orig,\n          l5: l5,\n        }\n      })\n    : null\n  var l7 = _vm.scoreModalVisible\n    ? _vm.__map(_vm.results, function (result, index) {\n        var $orig = _vm.__get_orig(result)\n        var g5 = _vm.selectedResults.includes(result.aiName)\n        return {\n          $orig: $orig,\n          g5: g5,\n        }\n      })\n    : null\n  var g6 = _vm.layoutModalVisible ? _vm.layoutPrompt.trim().length : null\n  var l8 = _vm.tthFlowVisible\n    ? _vm.__map(_vm.tthFlowLogs, function (log, index) {\n        var $orig = _vm.__get_orig(log)\n        var m17 = _vm.formatTime(log.timestamp)\n        return {\n          $orig: $orig,\n          m17: m17,\n        }\n      })\n    : null\n  var g7 = _vm.tthFlowVisible ? _vm.tthFlowLogs.length : null\n  var g8 = _vm.tthFlowVisible ? _vm.tthFlowImages.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l2: l2,\n        g2: g2,\n        l4: l4,\n        g4: g4,\n        m13: m13,\n        m14: m14,\n        m15: m15,\n        l6: l6,\n        l7: l7,\n        g6: g6,\n        l8: l8,\n        g7: g7,\n        g8: g8,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"console-container\">\r\n\t\t<!-- 顶部固定区域 -->\r\n\t\t<view class=\"header-fixed\">\r\n\t\t\t<view class=\"header-content\">\r\n\t\t\t\t<text class=\"header-title\">AI控制台</text>\r\n\t\t\t\t<view class=\"header-actions\">\r\n\t\t\t\t\t<view class=\"action-btn refresh-btn\" @tap=\"refreshAiStatus\">\r\n\t\t\t\t\t\t<image class=\"action-icon-img\" src=\"https://u3w.com/chatfile/shuaxin.png\" mode=\"aspectFit\">\r\n\t\t\t\t\t\t</image>\r\n\r\n            <!-- 连接状态指示器 -->\r\n            <view class=\"connection-indicator\" :class=\"[socketTask ? 'connected' : 'disconnected']\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"action-btn history-btn\" @tap=\"showHistoryDrawer\">\r\n\t\t\t\t\t\t<image class=\"action-icon-img\" src=\"https://u3w.com/chatfile/lishi.png\" mode=\"aspectFit\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"action-btn new-chat-btn\" @tap=\"createNewChat\">\r\n\t\t\t\t\t\t<image class=\"action-icon-img\" src=\"https://u3w.com/chatfile/chuangjian.png\" mode=\"aspectFit\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t</view>\r\n\r\n\t\t<!-- 主体滚动区域 -->\r\n\t\t<scroll-view class=\"main-scroll\" scroll-y :scroll-into-view=\"scrollIntoView\" :enhanced=\"true\" :bounces=\"true\"\r\n\t\t\t:show-scrollbar=\"false\" :fast-deceleration=\"false\">\r\n\r\n\t\t\t<!-- AI配置区块 -->\r\n\t\t\t<view class=\"section-block\" id=\"ai-config\">\r\n\t\t\t\t<view class=\"section-header\" @tap=\"toggleSection('aiConfig')\">\r\n\t\t\t\t\t<text class=\"section-title\">AI选择配置</text>\r\n\t\t\t\t\t<text class=\"section-arrow\">\r\n\t\t\t\t\t\t{{ sectionExpanded.aiConfig ? '▼' : '▶' }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"section-content\" v-if=\"sectionExpanded.aiConfig\">\r\n\t\t\t\t\t<view class=\"ai-grid\">\r\n\t\t\t\t\t\t<view v-for=\"(ai, index) in aiList\" :key=\"index\" class=\"ai-card\"\r\n\t\t\t\t\t\t\t:class=\"[ai.enabled && isAiLoginEnabled(ai) ? 'ai-enabled' : '', !isAiLoginEnabled(ai) ? 'ai-disabled' : '']\">\r\n\t\t\t\t\t\t\t<view class=\"ai-header\">\r\n\t\t\t\t\t\t\t\t<!-- <image class=\"ai-avatar\" :src=\"ai.avatar\" mode=\"aspectFill\" :class=\"[!isAiLoginEnabled(ai) ? 'avatar-disabled' : '']\"></image> -->\r\n\t\t\t\t\t\t\t\t<view class=\"ai-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"ai-name-container\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"ai-name\" :class=\"[!isAiLoginEnabled(ai) ? 'name-disabled' : '']\">{{\r\n\t\t\t\t\t\t\t\t\t\t\tai.name }}</text>\r\n                    <text\r\n                        v-if=\"!isAiLoginEnabled(ai) && !isAiInLoading(ai)\"\r\n                          class=\"login-required\"\r\n                    >\r\n                      需登录\r\n                    </text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"isAiInLoading(ai)\" class=\"loading-text\">检查中...</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<switch :checked=\"ai.enabled && isAiLoginEnabled(ai)\"\r\n\t\t\t\t\t\t\t\t\t\t:disabled=\"!isAiLoginEnabled(ai) || isAiInLoading(ai)\"\r\n\t\t\t\t\t\t\t\t\t\t@change=\"toggleAI(ai, $event)\" color=\"#409EFF\" style=\"transform: scale(0.8);\" />\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n              <view class=\"ai-capabilities\" v-if=\"ai.capabilities.length > 0\">\r\n                <!-- 通义千问使用单选按钮逻辑 -->\r\n                <view v-if=\"ai.name === '通义千问'\" class=\"capability-tags-container\">\r\n                  <view v-for=\"(capability, capIndex) in ai.capabilities\"\r\n                        :key=\"capIndex\"\r\n                        class=\"capability-tag\"\r\n                        :class=\"[ai.selectedCapability === capability.value ? 'capability-active' : '', (!ai.enabled || !isAiLoginEnabled(ai)) ? 'capability-disabled' : '']\"\r\n                        @tap=\"selectSingleCapability(ai, capability.value)\">\r\n                    <text class=\"capability-text\">{{ capability.label }}</text>\r\n                  </view>\r\n                </view>\r\n                <!-- 其他ai使用原有逻辑 -->\r\n                <view v-else class=\"capability-tags-container\">\r\n                  <view v-for=\"(capability, capIndex) in ai.capabilities\"\r\n                        :key=\"capIndex\"\r\n                        class=\"capability-tag\"\r\n                        :class=\"[ai.selectedCapabilities.includes(capability.value) ? 'capability-active' : '', (!ai.enabled || !isAiLoginEnabled(ai)) ? 'capability-disabled' : '']\"\r\n                        @tap=\"toggleCapability(ai, capability.value)\">\r\n                    <text class=\"capability-text\">{{ capability.label }}</text>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 提示词输入区块 -->\r\n\t\t\t<view class=\"section-block\" id=\"prompt-input\">\r\n\t\t\t\t<view class=\"section-header\" @tap=\"toggleSection('promptInput')\">\r\n\t\t\t\t\t<text class=\"section-title\">提示词输入</text>\r\n\t\t\t\t\t<text class=\"section-arrow\">\r\n\t\t\t\t\t\t{{ sectionExpanded.promptInput ? '▼' : '▶' }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"section-content\" v-if=\"sectionExpanded.promptInput\">\r\n\t\t\t\t\t<textarea class=\"prompt-textarea\" v-model=\"promptInput\" placeholder=\"请输入提示词\" maxlength=\"2000\"\r\n\t\t\t\t\t\tshow-confirm-bar=\"false\" auto-height></textarea>\r\n\t\t\t\t\t<view class=\"prompt-footer\">\r\n\t\t\t\t\t\t<text class=\"word-count\">{{ promptInput.length }}/2000</text>\r\n\t\t\t\t\t\t<button class=\"send-btn\" :class=\"[!canSend ? 'send-btn-disabled' : '']\" :disabled=\"!canSend\"\r\n\t\t\t\t\t\t\t@tap=\"sendPrompt\">\r\n\t\t\t\t\t\t\t发送\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 执行状态区块 -->\r\n\t\t\t<view class=\"section-block\" v-if=\"taskStarted\" id=\"task-status\">\r\n\t\t\t\t<view class=\"section-header\" @tap=\"toggleSection('taskStatus')\">\r\n\t\t\t\t\t<text class=\"section-title\">任务执行状态</text>\r\n\t\t\t\t\t<text class=\"section-arrow\">\r\n\t\t\t\t\t\t{{ sectionExpanded.taskStatus ? '▼' : '▶' }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"section-content\" v-if=\"sectionExpanded.taskStatus\">\r\n\t\t\t\t\t<!-- 任务流程 -->\r\n\t\t\t\t\t<view class=\"task-flow\">\r\n\t\t\t\t\t\t<view v-for=\"(ai, index) in enabledAIs\" :key=\"index\" class=\"task-item\">\r\n\t\t\t\t\t\t\t<view class=\"task-header\" @tap=\"toggleTaskExpansion(ai)\">\r\n\t\t\t\t\t\t\t\t<view class=\"task-left\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"task-arrow\">\r\n\t\t\t\t\t\t\t\t\t\t{{ ai.isExpanded ? '▼' : '▶' }}\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t<image class=\"task-avatar\" :src=\"ai.avatar\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class=\"task-name\">{{ ai.name }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"task-right\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"status-text\">{{ getStatusText(ai.status) }}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"status-icon\" :class=\"[getStatusIconClass(ai.status)]\">\r\n\t\t\t\t\t\t\t\t\t\t{{ getStatusEmoji(ai.status) }}\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 进度日志 -->\r\n\t\t\t\t\t\t\t<view class=\"progress-logs\" v-if=\"ai.isExpanded && ai.progressLogs.length > 0\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"(log, logIndex) in ai.progressLogs\" :key=\"logIndex\" class=\"progress-item\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"progress-dot\" :class=\"[log.isCompleted ? 'dot-completed' : '']\"></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"progress-content\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"progress-time\">{{ formatTime(log.timestamp) }}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"progress-text\">{{ log.content }}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 主机可视化 -->\r\n\t\t\t\t\t<!-- \t<view class=\"screenshots-section\" v-if=\"screenshots.length > 0\">\r\n\t\t\t\t\t\t<view class=\"screenshots-header\">\r\n\t\t\t\t\t\t\t<text class=\"section-subtitle\">主机可视化</text>\r\n\t\t\t\t\t\t\t<switch :checked=\"autoPlay\" @change=\"toggleAutoPlay\" color=\"#409EFF\"\r\n\t\t\t\t\t\t\t\tstyle=\"transform: scale(0.8);\" />\r\n\t\t\t\t\t\t\t<text class=\"auto-play-text\">自动轮播</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<swiper class=\"screenshots-swiper\" :autoplay=\"autoPlay\" :interval=\"3000\" :duration=\"500\"\r\n\t\t\t\t\t\t\tindicator-dots indicator-color=\"rgba(255,255,255,0.5)\" indicator-active-color=\"#409EFF\">\r\n\t\t\t\t\t\t\t<swiper-item v-for=\"(screenshot, index) in screenshots\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<image class=\"screenshot-image\" :src=\"screenshot\" mode=\"aspectFit\"\r\n\t\t\t\t\t\t\t\t\t@tap=\"previewImage(screenshot)\"></image>\r\n\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 结果展示区块 -->\r\n\t\t\t<view class=\"section-block\" v-if=\"results.length > 0\" id=\"results\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<text class=\"section-title\">执行结果</text>\r\n\t\t\t\t\t<button class=\"score-btn\" size=\"mini\" @tap=\"showScoreModal\">智能评分</button>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"section-content\">\r\n\t\t\t\t\t<!-- 结果选项卡 -->\r\n\t\t\t\t\t<scroll-view class=\"result-tabs\" scroll-x>\r\n\t\t\t\t\t\t<view class=\"tab-container\">\r\n\t\t\t\t\t\t\t<view v-for=\"(result, index) in results\" :key=\"index\" class=\"result-tab\"\r\n\t\t\t\t\t\t\t\t:class=\"[activeResultIndex === index ? 'tab-active' : '']\"\r\n\t\t\t\t\t\t\t\t@tap=\"switchResultTab(index)\">\r\n\t\t\t\t\t\t\t\t<text class=\"tab-text\">{{ result.aiName }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\r\n\t\t\t\t\t<!-- 结果内容 -->\r\n\t\t\t\t\t<view class=\"result-content\" v-if=\"currentResult\">\r\n\t\t\t\t\t\t<!-- 结果标题 -->\r\n\t\t\t\t\t\t<!-- <view class=\"result-header\">\r\n\t\t\t\t\t\t\t<text class=\"result-title\">{{ currentResult.aiName }}的执行结果</text>\r\n\t\t\t\t\t\t</view> -->\r\n\r\n\t\t\t\t\t\t<!-- 操作按钮 -->\r\n\t\t\t\t\t\t<view class=\"result-actions\">\r\n\t\t\t\t\t\t\t<button class=\"share-link-btn\" size=\"mini\" v-if=\"currentResult.shareUrl\"\r\n\t\t\t\t\t\t\t\t@tap=\"openShareUrl(currentResult.shareUrl)\">\r\n\t\t\t\t\t\t\t\t复制原链接\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t<button class=\"action-btn-small\" size=\"mini\"\r\n\t\t\t\t\t\t\t\t@tap=\"copyResult(currentResult.content)\">复制(纯文本)</button>\r\n\t\t\t\t\t\t\t<button class=\"collect-btn\" size=\"mini\"\r\n\t\t\t\t\t\t\t\t@tap=\"showLayoutModal\">投递到媒体</button>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 分享图片或内容 -->\r\n\t\t\t\t\t\t<view class=\"result-body\">\r\n\t\t\t\t\t\t\t<!-- 图片内容 -->\r\n\t\t\t\t\t\t\t<view v-if=\"currentResult.shareImgUrl && isImageFile(currentResult.shareImgUrl)\"\r\n\t\t\t\t\t\t\t\tclass=\"result-image-container\">\r\n\t\t\t\t\t\t\t\t<image class=\"result-image\" :src=\"currentResult.shareImgUrl\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\t\t\t@tap=\"previewImage(currentResult.shareImgUrl)\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- PDF文件内容 -->\r\n\t\t\t\t\t\t\t<view v-else-if=\"currentResult.shareImgUrl && isPdfFile(currentResult.shareImgUrl)\"\r\n\t\t\t\t\t\t\t\tclass=\"result-pdf-container\">\r\n\t\t\t\t\t\t\t\t<view class=\"pdf-placeholder\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"pdf-icon\">📄</view>\r\n\t\t\t\t\t\t\t\t\t<text class=\"pdf-text\">PDF文件</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"pdf-actions\">\r\n\t\t\t\t\t\t\t\t\t\t<button class=\"pdf-btn download-btn\" size=\"mini\"\r\n\t\t\t\t\t\t\t\t\t\t\t@tap=\"openPdfFile(currentResult.shareImgUrl)\">\r\n\t\t\t\t\t\t\t\t\t\t\t打开文件\r\n\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t<button class=\"pdf-btn copy-btn\" size=\"mini\"\r\n\t\t\t\t\t\t\t\t\t\t\t@tap=\"copyPdfUrl(currentResult.shareImgUrl)\">\r\n\t\t\t\t\t\t\t\t\t\t\t复制链接\r\n\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n              <!-- 文字内容 -->\r\n              <view v-else class=\"result-text\">\r\n                <!-- 特殊处理DeepSeek响应 -->\r\n                <rich-text v-if=\"currentResult.aiName === 'DeepSeek'\" :nodes=\"currentResult.content\"></rich-text>\r\n                <rich-text v-else :nodes=\"renderMarkdown(currentResult.content)\"></rich-text>\r\n              </view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\r\n\t\t<!-- 历史记录抽屉 -->\r\n\t\t<view v-if=\"historyDrawerVisible\" class=\"drawer-mask\" @tap=\"closeHistoryDrawer\">\r\n\t\t\t<view class=\"drawer-container\" @tap.stop>\r\n\t\t\t\t<view class=\"drawer-content\">\r\n\t\t\t\t\t<view class=\"drawer-header\">\r\n\t\t\t\t\t\t<text class=\"drawer-title\">历史会话记录</text>\r\n\t\t\t\t\t\t<text class=\"close-icon\" @tap=\"closeHistoryDrawer\">✕</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<scroll-view class=\"history-list\" scroll-y>\r\n\t\t\t\t\t\t<view v-for=\"(group, date) in groupedHistory\" :key=\"date\" class=\"history-group\">\r\n\t\t\t\t\t\t\t<text class=\"history-date\">{{ date }}</text>\r\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in group\" :key=\"index\" class=\"history-item\"\r\n\t\t\t\t\t\t\t\t@tap=\"loadHistoryItem(item)\">\r\n\t\t\t\t\t\t\t\t<text class=\"history-prompt\">{{ item.userPrompt }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"history-time\">{{ formatHistoryTime(item.createTime) }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 智能评分弹窗 -->\r\n\t\t<view v-if=\"scoreModalVisible\" class=\"popup-mask\" @tap=\"closeScoreModal\">\r\n\t\t\t<view class=\"score-modal\" @tap.stop>\r\n\t\t\t\t<view class=\"score-header\">\r\n\t\t\t\t\t<text class=\"score-title\">智能评分</text>\r\n\t\t\t\t\t<text class=\"close-icon\" @tap=\"closeScoreModal\">✕</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"score-content\">\r\n\t\t\t\t\t<view class=\"score-prompt-section\">\r\n\t\t\t\t\t\t<text class=\"score-subtitle\">评分提示词：</text>\r\n\t\t\t\t\t\t<textarea class=\"score-textarea\" v-model=\"scorePrompt\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分\" maxlength=\"1000\"></textarea>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"score-selection\">\r\n\t\t\t\t\t\t<text class=\"score-subtitle\">选择要评分的内容：</text>\r\n\t\t\t\t\t\t<checkbox-group @change=\"toggleResultSelection\">\r\n\t\t\t\t\t\t\t<view class=\"score-checkboxes\">\r\n\t\t\t\t\t\t\t\t<label v-for=\"(result, index) in results\" :key=\"index\" class=\"checkbox-item\">\r\n\t\t\t\t\t\t\t\t\t<checkbox :value=\"result.aiName\"\r\n\t\t\t\t\t\t\t\t\t\t:checked=\"selectedResults.includes(result.aiName)\" />\r\n\t\t\t\t\t\t\t\t\t<text class=\"checkbox-text\">{{ result.aiName }}</text>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<button class=\"score-submit-btn\" :disabled=\"!canScore\" @tap=\"handleScore\">\r\n\t\t\t\t\t\t开始评分\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n    <!-- 媒体投递弹窗 -->\r\n    <view v-if=\"layoutModalVisible\" class=\"popup-mask\" @tap=\"closeLayoutModal\">\r\n      <view class=\"score-modal\" @tap.stop>\r\n        <view class=\"score-header\">\r\n          <text class=\"score-title\">媒体投递设置</text>\r\n          <text class=\"close-icon\" @tap=\"closeLayoutModal\">✕</text>\r\n        </view>\r\n        <view class=\"score-content\">\r\n          <!-- 媒体选择 -->\r\n          <view class=\"media-selection-section\">\r\n            <text class=\"score-subtitle\">选择投递媒体：</text>\r\n            <view class=\"media-radio-group\">\r\n              <view class=\"media-radio-item\"\r\n                    :class=\"{'active': selectedMedia === 'wechat'}\"\r\n                    @tap=\"selectMedia('wechat')\">\r\n                <text class=\"media-icon\">📱</text>\r\n                <text class=\"media-text\">公众号</text>\r\n              </view>\r\n              <view class=\"media-radio-item\"\r\n                    :class=\"{'active': selectedMedia === 'zhihu'}\"\r\n                    @tap=\"selectMedia('zhihu')\">\r\n                <text class=\"media-icon\">📖</text>\r\n                <text class=\"media-text\">知乎</text>\r\n              </view>\r\n              <view class=\"media-radio-item\"\r\n                    :class=\"{'active': selectedMedia === 'toutiao'}\"\r\n                    @tap=\"selectMedia('toutiao')\">\r\n                <text class=\"media-icon\">📰</text>\r\n                <text class=\"media-text\">微头条</text>\r\n              </view>\r\n\t\t\t  <view class=\"media-radio-item\"\r\n\t\t\t        :class=\"{'active': selectedMedia === 'baijiahao'}\"\r\n\t\t\t        @tap=\"selectMedia('baijiahao')\">\r\n\t\t\t    <text class=\"media-icon\">🔈</text>\r\n\t\t\t    <text class=\"media-text\">百家号</text>\r\n\t\t\t  </view>\r\n            </view>\r\n            <view class=\"media-description\">\r\n              <text v-if=\"selectedMedia === 'wechat'\" class=\"description-text\">\r\n                📝 将内容排版为适合微信公众号的HTML格式，并自动投递到草稿箱\r\n              </text>\r\n              <text v-else-if=\"selectedMedia === 'zhihu'\" class=\"description-text\">\r\n                📖 将内容转换为知乎专业文章格式，直接投递到知乎草稿箱\r\n              </text>\r\n              <text v-else-if=\"selectedMedia === 'toutiao'\" class=\"description-text\">\r\n                📰 将内容排版为适合微头条的文章格式，并发布到微头条\r\n              </text>\r\n\t\t\t  <text v-else-if=\"selectedMedia === 'baijiahao'\" class=\"description-text\">\r\n\t\t\t    🔈 将内容排版为适合百家号的帖子格式，并发布到百家号草稿箱\r\n\t\t\t  </text>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"score-prompt-section\">\r\n            <text class=\"score-subtitle\">排版提示词：</text>\r\n            <textarea class=\"score-textarea\" v-model=\"layoutPrompt\"\r\n                      placeholder=\"请输入排版要求\" maxlength=\"100000\" :rows=\"10\"></textarea>\r\n          </view>\r\n\r\n          <button class=\"score-submit-btn\" :disabled=\"layoutPrompt.trim().length === 0\" @tap=\"handleLayout\">\r\n            排版后智能投递\r\n          </button>\r\n        </view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 微头条文章编辑弹窗 -->\r\n\t\t<view v-if=\"tthArticleEditVisible\" class=\"popup-mask\" @tap=\"closeTthArticleEditModal\">\r\n\t\t\t<view class=\"score-modal\" @tap.stop>\r\n\t\t\t\t<view class=\"score-header\">\r\n\t\t\t\t\t<text class=\"score-title\">微头条文章编辑</text>\r\n\t\t\t\t\t<text class=\"close-icon\" @tap=\"closeTthArticleEditModal\">✕</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"score-content\">\r\n\t\t\t\t\t<view class=\"score-prompt-section\">\r\n\t\t\t\t\t\t<text class=\"score-subtitle\">文章标题：</text>\r\n\t\t\t\t\t\t<input type=\"text\" v-model=\"tthArticleTitle\" placeholder=\"请输入文章标题\" maxlength=\"100\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"score-prompt-section\">\r\n\t\t\t\t\t\t<text class=\"score-subtitle\">文章内容：</text>\r\n\t\t\t\t\t\t<textarea \r\n\t\t\t\t\t\t\tclass=\"score-textarea\" \r\n\t\t\t\t\t\t\t:class=\"{ 'content-exceeded': isTthArticleContentExceeded }\"\r\n\t\t\t\t\t\t\tv-model=\"tthArticleContent\" \r\n\t\t\t\t\t\t\tplaceholder=\"请输入文章内容\" \r\n\t\t\t\t\t\t\t:maxlength=\"-1\"\r\n\t\t\t\t\t\t\t:auto-height=\"true\"\r\n\t\t\t\t\t\t\t:show-confirm-bar=\"false\"\r\n\t\t\t\t\t\t\t:hold-keyboard=\"true\"\r\n\t\t\t\t\t\t\t:adjust-position=\"false\"\r\n\t\t\t\t\t\t\t@focus=\"handleTextareaFocus\"\r\n\t\t\t\t\t\t\trows=\"5\">\r\n\t\t\t\t\t\t</textarea>\r\n\t\t\t\t\t\t<view class=\"char-count\" :class=\"{ 'char-count-exceeded': isTthArticleContentExceeded }\">\r\n\t\t\t\t\t\t\t{{ tthArticleContentLength }}/2000\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"score-submit-btn\" @tap=\"confirmTTHPublish\">\r\n\t\t\t\t\t\t发布文章\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 微头条发布流程弹窗 -->\r\n\t\t<view v-if=\"tthFlowVisible\" class=\"popup-mask\" @tap=\"closeTthFlowDialog\">\r\n\t\t\t<view class=\"score-modal\" @tap.stop>\r\n\t\t\t\t<view class=\"score-header\">\r\n\t\t\t\t\t<text class=\"score-title\">微头条发布流程</text>\r\n\t\t\t\t\t<text class=\"close-icon\" @tap=\"closeTthFlowDialog\">✕</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"score-content\">\r\n\t\t\t\t\t<view class=\"score-prompt-section\">\r\n\t\t\t\t\t\t<text class=\"score-subtitle\">发布流程日志：</text>\r\n\t\t\t\t\t\t<scroll-view style=\"max-height: 200px;\" scroll-y>\r\n\t\t\t\t\t\t\t<view v-for=\"(log, index) in tthFlowLogs\" :key=\"index\" style=\"margin-bottom: 10px;\">\r\n\t\t\t\t\t\t\t\t<text style=\"color: #666;\">{{ formatTime(log.timestamp) }}</text>\r\n\t\t\t\t\t\t\t\t<text style=\"margin-left: 10px;\">{{ log.content }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-if=\"tthFlowLogs.length === 0\" style=\"text-align: center; color: #999; padding: 20px;\">暂无流程日志...</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"score-prompt-section\" v-if=\"tthFlowImages.length > 0\">\r\n\t\t\t\t\t\t<text class=\"score-subtitle\">发布流程图片：</text>\r\n\t\t\t\t\t\t<scroll-view style=\"max-height: 200px;\" scroll-x>\r\n\t\t\t\t\t\t\t<image v-for=\"(img, idx) in tthFlowImages\" :key=\"idx\" :src=\"img\" style=\"width: 120px; height: 120px; margin-right: 10px; border-radius: 8px;\" mode=\"aspectFill\" @tap=\"previewImage(img)\" />\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"display: flex; justify-content: center; margin-top: 20px;\">\r\n\t\t\t\t\t\t<button class=\"score-submit-btn\" style=\"width: 200px;\" @tap=\"closeTthFlowDialog\">关闭</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmarked\r\n\t} from 'marked';\r\n\timport {\r\n\t\tmessage, saveUserChatData, getChatHistory,pushAutoOffice, getMediaCallWord, updateMediaCallWord\r\n  } from \"@/api/wechat/aigc\";\r\n\timport {\r\n\t\tv4 as uuidv4\r\n\t} from 'uuid';\r\n\timport storage from '@/utils/storage'\r\n\timport constant from '@/utils/constant'\r\n  import { getToken } from '@/utils/auth';\r\n\r\n\texport default {\r\n\t\tname: 'MiniConsole',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 用户信息\r\n\t\t\t\tuserId: '',\r\n\t\t\t\tcorpId: '',\r\n\t\t\t\tchatId: '',\r\n\t\t\t\texpandedHistoryItems: {},\r\n\t\t\t\tuserInfoReq: {\r\n\t\t\t\t\tuserPrompt: '',\r\n\t\t\t\t\tuserId: '',\r\n\t\t\t\t\tcorpId: '',\r\n\t\t\t\t\ttaskId: '',\r\n\t\t\t\t\troles: '',\r\n\t\t\t\t\ttoneChatId: '',\r\n\t\t\t\t\tybDsChatId: '',\r\n\t\t\t\t\tdbChatId: '',\r\n          tyChatId: '',\r\n\t\t\t\t\tisNewChat: true\r\n\t\t\t\t},\r\n\t\t\t\tjsonRpcReqest: {\r\n\t\t\t\t\tjsonrpc: '2.0',\r\n\t\t\t\t\tid: '',\r\n\t\t\t\t\tmethod: '',\r\n\t\t\t\t\tparams: {}\r\n\t\t\t\t},\r\n\r\n\t\t\t\t// 区域展开状态\r\n\t\t\t\tsectionExpanded: {\r\n\t\t\t\t\taiConfig: true,\r\n\t\t\t\t\tpromptInput: true,\r\n\t\t\t\t\ttaskStatus: true\r\n\t\t\t\t},\r\n\r\n\t\t\t\t// AI配置（参考PC端完整配置）\r\n\t\t\t\taiList: [{\r\n            name: 'DeepSeek',\r\n            avatar: 'https://communication.cn-nb1.rains3.com/Deepseek.png',\r\n            capabilities: [{\r\n              label: '深度思考',\r\n              value: 'deep_thinking'\r\n            },\r\n              {\r\n                label: '联网搜索',\r\n                value: 'web_search'\r\n              }\r\n            ],\r\n            selectedCapabilities: ['deep_thinking', 'web_search'],\r\n            enabled: true,\r\n            status: 'idle',\r\n            progressLogs: [],\r\n            isExpanded: true\r\n          },\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '豆包',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\r\n\t\t\t\t\t\tcapabilities: [{\r\n\t\t\t\t\t\t\tlabel: '深度思考',\r\n\t\t\t\t\t\t\tvalue: 'deep_thinking'\r\n\t\t\t\t\t\t}],\r\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking'],\r\n\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\tstatus: 'idle',\r\n\t\t\t\t\t\tprogressLogs: [],\r\n\t\t\t\t\t\tisExpanded: true\r\n\t\t\t\t\t},\r\n          {\r\n            name: '通义千问',\r\n            avatar: 'https://u3w.com/chatfile/TongYi.png',\r\n            capabilities: [\r\n              {\r\n                label: '深度思考',\r\n                value: 'deep_thinking'\r\n              },\r\n              {\r\n                label: '联网搜索',\r\n                value: 'web_search'\r\n              }\r\n            ],\r\n            selectedCapability: '',\r\n            enabled: true,\r\n            status: 'idle',\r\n            progressLogs: [],\r\n            isExpanded: true\r\n          },\r\n          {\r\n            name: \"MiniMax Chat\",\r\n            avatar: 'https://u3w.com/chatfile/MiniMax.png',\r\n            capabilities: [\r\n              { label: \"深度思考\", value: \"deep_thinking\" },\r\n              { label: \"联网搜索\", value: \"web_search\" },\r\n            ],\r\n            selectedCapabilities: [],\r\n            enabled: true,\r\n            status: \"idle\",\r\n            progressLogs: [],\r\n            isExpanded: true,\r\n          },\r\n\t\t\t\t],\r\n\r\n\t\t\t\t// 输入和任务状态\r\n\t\t\t\tpromptInput: '',\r\n\t\t\t\ttaskStarted: false,\r\n\t\t\t\tenabledAIs: [],\r\n\r\n\t\t\t\t// 可视化\r\n\t\t\t\tscreenshots: [],\r\n\t\t\t\tautoPlay: false,\r\n\r\n\t\t\t\t// 结果\r\n\t\t\t\tresults: [],\r\n\t\t\t\tactiveResultIndex: 0,\r\n\r\n\t\t\t\t// 历史记录\r\n\t\t\t\tchatHistory: [],\r\n\r\n\t\t\t\t// 评分\r\n\t\t\t\tselectedResults: [],\r\n\t\t\t\tscorePrompt: '请你深度阅读以下几篇公众号文章，从多个维度进行逐项打分，输出评分结果。并在以下各篇文章的基础上博采众长，综合整理一篇更全面的文章。',\r\n\r\n\t\t\t\t// 收录计数器\r\n\t\t\t\tcollectNum: 0,\r\n\r\n\t\t\t\t// 媒体投递\r\n        layoutPrompt: '',\r\n        selectedMedia: 'wechat', // 默认选择公众号\r\n\r\n\t\t\t\t// 微头条相关\r\n\t\t\t\ttthArticleEditVisible: false, // 微头条文章编辑弹窗\r\n\t\t\t\ttthArticleTitle: '', // 微头条文章标题\r\n\t\t\t\ttthArticleContent: '', // 微头条文章内容\r\n\t\t\t\ttthFlowVisible: false, // 微头条发布流程弹窗\r\n\t\t\t\ttthFlowLogs: [], // 微头条发布流程日志\r\n\t\t\t\ttthFlowImages: [], // 微头条发布流程图片\r\n\t\t\t\ttthScoreContent: '', // 智能评分内容\r\n\r\n\t\t\t\t// WebSocket\r\n\t\t\t\tsocketTask: null,\r\n\t\t\t\treconnectTimer: null,\r\n\t\t\t\theartbeatTimer: null,\r\n\t\t\t\treconnectCount: 0,\r\n\t\t\t\tmaxReconnectCount: 5,\r\n\t\t\t\tisConnecting: false,\r\n\t\t\t\tscrollIntoView: '',\r\n\r\n\t\t\t\t// 弹窗状态\r\n\t\t\t\thistoryDrawerVisible: false,\r\n\t\t\t\tscoreModalVisible: false,\r\n\t\t\t\tlayoutModalVisible: false,\r\n\t\t\t\tcurrentLayoutResult: null, // 当前要排版的结果\r\n\r\n\t\t\t\t// AI登录状态\r\n\t\t\t\taiLoginStatus: {\r\n\t\t\t\t\tdoubao: false,\r\n          deepseek: false,\r\n          tongyi: false,\r\n          mini: false,\r\n\t\t\t\t},\r\n\t\t\t\taccounts: {\r\n\t\t\t\t\tdoubao: '',\r\n          deepseek: '',\r\n          tongyi: '',\r\n          mini: '',\r\n\t\t\t\t},\r\n\t\t\t\tisLoading: {\r\n\t\t\t\t\tdoubao: true,\r\n          deepseek: true,\r\n          tongyi: true,\r\n\t\t      mini: true,\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tcomputed: {\r\n\t\t\tcanSend() {\r\n\t\t\t\t// 检查是否有输入内容\r\n\t\t\t\tconst hasInput = this.promptInput.trim().length > 0;\r\n\r\n\t\t\t\t// 检查是否有可用的AI（既启用又已登录）\r\n\t\t\t\tconst hasAvailableAI = this.aiList.some(ai => ai.enabled && this.isAiLoginEnabled(ai));\r\n\r\n\t\t\t\t// 检查是否正在加载AI状态（如果正在加载，禁用发送按钮）\r\n\t\t\t\tconst isCheckingStatus = this.isLoading.doubao || this.isLoading.deepseek || this.isLoading.tongyi || this.isLoading.mini;\r\n\r\n\t\t\t\treturn hasInput && hasAvailableAI && !isCheckingStatus;\r\n\t\t\t},\r\n\r\n\t\t\tcanScore() {\r\n\t\t\t\tconst hasSelected = this.selectedResults.length > 0;\r\n\t\t\t\tconst hasPrompt = this.scorePrompt.trim().length > 0;\r\n\t\t\t\tconsole.log('canScore - selectedResults:', this.selectedResults);\r\n\t\t\t\tconsole.log('canScore - scorePrompt length:', this.scorePrompt.trim().length);\r\n\t\t\t\tconsole.log('canScore - hasSelected:', hasSelected, 'hasPrompt:', hasPrompt);\r\n\t\t\t\treturn hasSelected && hasPrompt;\r\n\t\t\t},\r\n\r\n\t\t\tcurrentResult() {\r\n\t\t\t\treturn this.results[this.activeResultIndex] || null;\r\n\t\t\t},\r\n\r\n\t\t\tgroupedHistory() {\r\n\t\t\t\tconst groups = {};\r\n\t\t\t\tconst chatGroups = {};\r\n\r\n\t\t\t\t// 首先按chatId分组\r\n\t\t\t\tthis.chatHistory.forEach(item => {\r\n\t\t\t\t\tif (!chatGroups[item.chatId]) {\r\n\t\t\t\t\t\tchatGroups[item.chatId] = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tchatGroups[item.chatId].push(item);\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 然后按日期分组，并处理父子关系\r\n\t\t\t\tObject.values(chatGroups).forEach(chatGroup => {\r\n\t\t\t\t\t// 按时间排序\r\n\t\t\t\t\tchatGroup.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));\r\n\r\n\t\t\t\t\t// 获取最早的记录作为父级\r\n\t\t\t\t\tconst parentItem = chatGroup[0];\r\n\t\t\t\t\tconst date = this.getHistoryDate(parentItem.createTime);\r\n\r\n\t\t\t\t\tif (!groups[date]) {\r\n\t\t\t\t\t\tgroups[date] = [];\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 添加父级记录\r\n\t\t\t\t\tgroups[date].push({\r\n\t\t\t\t\t\t...parentItem,\r\n\t\t\t\t\t\tisParent: true,\r\n\t\t\t\t\t\tisExpanded: this.expandedHistoryItems[parentItem.chatId] || false,\r\n\t\t\t\t\t\tchildren: chatGroup.slice(1).map(child => ({\r\n\t\t\t\t\t\t\t...child,\r\n\t\t\t\t\t\t\tisParent: false\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\r\n\t\t\t\treturn groups;\r\n\t\t\t},\r\n\r\n\t\t\t// 微头条文章内容字符数\r\n\t\t\ttthArticleContentLength() {\r\n\t\t\t\treturn this.tthArticleContent ? this.tthArticleContent.length : 0;\r\n\t\t\t},\r\n\r\n\t\t\t// 检查微头条文章内容是否超过2000字\r\n\t\t\tisTthArticleContentExceeded() {\r\n\t\t\t\treturn this.tthArticleContentLength > 2000;\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 监听微头条文章内容变化，确保textarea正确显示\r\n\t\t\ttthArticleContent: {\r\n\t\t\t\thandler(newVal, oldVal) {\r\n\t\t\t\t\t// 当内容变化时，确保textarea正确显示\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tconst textarea = this.$el.querySelector('.score-textarea');\r\n\t\t\t\t\t\tif (textarea && textarea.value !== newVal) {\r\n\t\t\t\t\t\t\ttextarea.value = newVal;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\timmediate: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.initUserInfo();\r\n\r\n\t\t\t// 检查用户信息是否完整\r\n\t\t\tif (!this.userId || !this.corpId) {\r\n\t\t\t\tconsole.log('用户信息不完整，跳转到登录页面');\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '请先登录后再使用',\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tconfirmText: '去登录',\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/login/index'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.initWebSocket();\r\n\t\t\tthis.loadChatHistory(0); // 加载历史记录\r\n\t\t\tthis.loadLastChat(); // 加载上次会话\r\n\t\t\tthis.checkAiLoginStatus(); // 检查AI登录状态\r\n\t\t},\r\n\r\n\t\tonUnload() {\r\n\t\t\tthis.closeWebSocket();\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 处理textarea获得焦点事件\r\n\t\t\thandleTextareaFocus() {\r\n\t\t\t\t// 确保textarea内容正确显示\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tconst textarea = this.$el.querySelector('.score-textarea');\r\n\t\t\t\t\tif (textarea && textarea.value !== this.tthArticleContent) {\r\n\t\t\t\t\t\ttextarea.value = this.tthArticleContent;\r\n\t\t\t\t\t\t// 触发input事件确保v-model同步\r\n\t\t\t\t\t\ttextarea.dispatchEvent(new Event('input', { bubbles: true }));\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 初始化用户信息\r\n\t\t\tinitUserInfo() {\r\n\t\t\t\t// 从store获取用户信息，兼容缓存方式\r\n\t\t\tthis.userId = storage.get(constant.userId);\r\n\t\t\tthis.corpId = storage.get(constant.corpId);\r\n\r\n\t\t\t\tthis.chatId = this.generateUUID();\r\n\r\n\t\t\t\t// 初始化请求参数\r\n\t\t\t\tthis.userInfoReq.userId = this.userId;\r\n\t\t\t\tthis.userInfoReq.corpId = this.corpId;\r\n\r\n\t\t\t\tconsole.log('初始化用户信息:', {\r\n\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\tcorpId: this.corpId\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 生成UUID\r\n\t\t\tgenerateUUID() {\r\n\t\t\t\treturn uuidv4();\r\n\t\t\t},\r\n\r\n\t\t\t// 切换区域展开状态\r\n\t\t\ttoggleSection(section) {\r\n\t\t\t\tthis.sectionExpanded[section] = !this.sectionExpanded[section];\r\n\t\t\t},\r\n\r\n\t\t\t// 切换AI启用状态\r\n\t\t\ttoggleAI(ai, event) {\r\n\t\t\t\t// 检查AI是否已登录\r\n\t\t\t\tif (!this.isAiLoginEnabled(ai)) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: `${ai.name}需要先登录，请前往PC端进行登录后再使用`,\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tconfirmText: '知道了'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tai.enabled = event.detail.value;\r\n\t\t\t},\r\n\r\n\t\t\t// 切换AI能力\r\n\t\t\ttoggleCapability(ai, capabilityValue) {\r\n\t\t\t\t// 检查AI是否已登录和启用\r\n\t\t\t\tif (!this.isAiLoginEnabled(ai)) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: `${ai.name}需要先登录，请前往PC端进行登录后再使用`,\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tconfirmText: '知道了'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!ai.enabled) return;\r\n\r\n\t\t\t\tconst index = ai.selectedCapabilities.indexOf(capabilityValue);\r\n\t\t\t\tif (index === -1) {\r\n\t\t\t\t\tai.selectedCapabilities.push(capabilityValue);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tai.selectedCapabilities.splice(index, 1);\r\n\t\t\t\t}\r\n\t\t\t},\r\n      // 通义千问切换能力\r\n      selectSingleCapability(ai, capabilityValue) {\r\n        if (!ai.enabled || !this.isAiLoginEnabled(ai)) return;\r\n\r\n        if (ai.selectedCapability === capabilityValue) {\r\n          ai.selectedCapability = '';\r\n        } else {\r\n          ai.selectedCapability = capabilityValue;\r\n        }\r\n      },\r\n\r\n\t\t\t// 发送提示词\r\n\t\t\tsendPrompt() {\r\n\t\t\t\tif (!this.canSend) return;\r\n\r\n\t\t\t\tthis.screenshots = [];\r\n\t\t\t\t// 折叠所有区域\r\n\t\t\t\tthis.sectionExpanded.aiConfig = false;\r\n\t\t\t\tthis.sectionExpanded.promptInput = false;\r\n\t\t\t\t// this.sectionExpanded.taskStatus = false;\r\n\r\n\t\t\t\tthis.taskStarted = true;\r\n\t\t\t\tthis.results = []; // 清空之前的结果\r\n\r\n\t\t\t\tthis.userInfoReq.roles = '';\r\n\t\t\t\tthis.userInfoReq.taskId = this.generateUUID();\r\n\t\t\t\tthis.userInfoReq.userId = this.userId;\r\n\t\t\t\tthis.userInfoReq.corpId = this.corpId;\r\n\t\t\t\tthis.userInfoReq.userPrompt = this.promptInput;\r\n\r\n\t\t\t\t// 获取启用的AI列表及其状态\r\n\t\t\t\tthis.enabledAIs = this.aiList.filter(ai => ai.enabled  && this.isAiLoginEnabled(ai));\r\n\r\n\t\t\t\t// 将所有启用的AI状态设置为运行中\r\n\t\t\t\tthis.enabledAIs.forEach(ai => {\r\n\t\t\t\t\tai.status = 'running';\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 构建角色参数\r\n\t\t\t\tthis.enabledAIs.forEach(ai => {\r\n          if (ai.name === 'DeepSeek') {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'deepseek,';\r\n            if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n              this.userInfoReq.roles = this.userInfoReq.roles + 'ds-sdsk,';\r\n            }\r\n            if (ai.selectedCapabilities.includes(\"web_search\")) {\r\n              this.userInfoReq.roles = this.userInfoReq.roles + 'ds-lwss,';\r\n            }\r\n          }\r\n\t\t\t\t\tif (ai.name === '豆包') {\r\n\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'zj-db,';\r\n\t\t\t\t\t\tif (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n\t\t\t\t\t\t\tthis.userInfoReq.roles = this.userInfoReq.roles + 'zj-db-sdsk,';\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (ai.name === \"MiniMax Chat\") {\r\n\t\t\t\t\t\tif(this.isAiLoginEnabled(ai)){\r\n\t\t\t\t\t\t  this.userInfoReq.roles = this.userInfoReq.roles + \"mini-max-agent,\";\r\n\t\t\t\t\t\tif (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n\t\t\t\t\t\t  this.userInfoReq.roles = this.userInfoReq.roles + \"max-sdsk,\";\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (ai.selectedCapabilities.includes(\"web_search\")) {\r\n\t\t\t\t\t\t  this.userInfoReq.roles = this.userInfoReq.roles + \"max-lwss,\";\r\n\t\t\t\t\t\t}\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n          if(ai.name === '通义千问' && ai.enabled){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw,';\r\n            if (ai.selectedCapability.includes(\"deep_thinking\")) {\r\n              this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw-sdsk,'\r\n            } else if (ai.selectedCapability.includes(\"web_search\")) {\r\n              this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw-lwss,';\r\n            }\r\n          }\r\n\t\t\t\t});\r\n\r\n\t\t\t\tconsole.log(\"参数：\", this.userInfoReq);\r\n\r\n\t\t\t\t// 滚动到任务状态区域\r\n\t\t\t\tthis.scrollIntoView = 'task-status';\r\n\r\n\t\t\t\t//调用后端接口\r\n\t\t\t\tthis.jsonRpcReqest.id = this.generateUUID();\r\n\t\t\t\tthis.jsonRpcReqest.method = \"使用F8S\";\r\n\t\t\t\tthis.jsonRpcReqest.params = this.userInfoReq;\r\n\t\t\t\tthis.message(this.jsonRpcReqest);\r\n\t\t\t\tthis.userInfoReq.isNewChat = false;\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '任务已提交',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t\t\t// WebSocket相关方法\r\n\t\tinitWebSocket() {\r\n\t\t\t// 检查用户信息是否完整\r\n\t\t\tif (!this.userId || !this.corpId) {\r\n\t\t\t\tconsole.log('用户信息不完整，跳转到登录页面');\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '请先登录后再使用',\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tconfirmText: '去登录',\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/login/index'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tif (this.isConnecting) {\r\n\t\t\t\tconsole.log('WebSocket正在连接中，跳过重复连接');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.isConnecting = true;\r\n\r\n\t\t\t// 使用PC端的WebSocket连接方式\r\n\t\t    // const wsUrl = `${process.env.VUE_APP_WS_API || 'wss://u3w.com/cubeServer/websocket?clientId='}mypc-${this.userId}`;\r\n\t\t\tconst wsUrl = `${process.env.VUE_APP_WS_API || 'ws://127.0.0.1:8081/websocket?clientId='}mypc-${this.userId}`;\r\n\t\t\tconsole.log('WebSocket URL:', wsUrl);\r\n\r\n\t\t\tthis.socketTask = uni.connectSocket({\r\n\t\t\t\turl: wsUrl,\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tconsole.log('WebSocket连接成功');\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.error('WebSocket连接失败', err);\r\n\t\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\t\tthis.handleReconnect();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\tthis.socketTask.onOpen(() => {\r\n\t\t\t\tconsole.log('WebSocket连接已打开');\r\n\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\tthis.reconnectCount = 0; // 重置重连次数\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '连接成功',\r\n\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 开始心跳检测\r\n\t\t\t\tthis.startHeartbeat();\r\n\t\t\t});\r\n\r\n\t\t\tthis.socketTask.onMessage((res) => {\r\n\t\t\t\tthis.handleWebSocketMessage(res.data);\r\n\t\t\t});\r\n\r\n\t\t\tthis.socketTask.onError((err) => {\r\n\t\t\t\tconsole.error('WebSocket连接错误', err);\r\n\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: 'WebSocket连接错误',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\tthis.handleReconnect();\r\n\t\t\t});\r\n\r\n\t\t\tthis.socketTask.onClose(() => {\r\n\t\t\t\tconsole.log('WebSocket连接已关闭');\r\n\t\t\t\tthis.isConnecting = false;\r\n\t\t\t\tthis.stopHeartbeat(); // 停止心跳\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: 'WebSocket连接已关闭',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 尝试重连\r\n\t\t\t\tthis.handleReconnect();\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 处理重连\r\n\t\thandleReconnect() {\r\n\t\t\tif (this.reconnectCount >= this.maxReconnectCount) {\r\n\t\t\t\tconsole.log('WebSocket重连次数已达上限');\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '连接失败',\r\n\t\t\t\t\tcontent: '网络连接不稳定，请检查网络后手动刷新页面',\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tconfirmText: '知道了'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.reconnectCount++;\r\n\t\t\tconst delay = Math.min(1000 * Math.pow(2, this.reconnectCount), 30000); // 指数退避，最大30秒\r\n\r\n\t\t\tconsole.log(`WebSocket将在${delay}ms后进行第${this.reconnectCount}次重连`);\r\n\r\n\t\t\tthis.reconnectTimer = setTimeout(() => {\r\n\t\t\t\tconsole.log(`开始第${this.reconnectCount}次重连`);\r\n\t\t\t\tthis.initWebSocket();\r\n\t\t\t}, delay);\r\n\t\t},\r\n\r\n\t\t// 开始心跳检测\r\n\t\tstartHeartbeat() {\r\n\t\t\tthis.stopHeartbeat(); // 先停止之前的心跳\r\n\r\n\t\t\tthis.heartbeatTimer = setInterval(() => {\r\n\t\t\t\tif (this.socketTask) {\r\n\t\t\t\t\tthis.sendWebSocketMessage({\r\n\t\t\t\t\t\ttype: 'HEARTBEAT',\r\n\t\t\t\t\t\ttimestamp: Date.now()\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}, 30000); // 每30秒发送一次心跳\r\n\t\t},\r\n\r\n\t\t// 停止心跳检测\r\n\t\tstopHeartbeat() {\r\n\t\t\tif (this.heartbeatTimer) {\r\n\t\t\t\tclearInterval(this.heartbeatTimer);\r\n\t\t\t\tthis.heartbeatTimer = null;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t\tsendWebSocketMessage(data) {\r\n\t\t\t\tif (this.socketTask) {\r\n\t\t\t\t\tthis.socketTask.send({\r\n\t\t\t\t\t\tdata: JSON.stringify(data)\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn('WebSocket未连接，无法发送消息');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 调用后端message接口\r\n\t\t\tmessage(data) {\r\n\t\t\t\tmessage(data).then(res => {\r\n\t\t\t\t\tif (res.code == 201) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.messages,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t\t\tcloseWebSocket() {\r\n\t\t\t// 清理重连定时器\r\n\t\t\tif (this.reconnectTimer) {\r\n\t\t\t\tclearTimeout(this.reconnectTimer);\r\n\t\t\t\tthis.reconnectTimer = null;\r\n\t\t\t}\r\n\r\n\t\t\t// 停止心跳检测\r\n\t\t\tthis.stopHeartbeat();\r\n\r\n\t\t\t// 关闭WebSocket连接\r\n\t\t\tif (this.socketTask) {\r\n\t\t\t\tthis.socketTask.close();\r\n\t\t\t\tthis.socketTask = null;\r\n\t\t\t}\r\n\r\n\t\t\t// 重置状态\r\n\t\t\tthis.isConnecting = false;\r\n\t\t\tthis.reconnectCount = 0;\r\n\t\t},\r\n\r\n\t\t\t\t\t// 处理WebSocket消息\r\n\t\thandleWebSocketMessage(data) {\r\n\t\t\ttry {\r\n\t\t\t\tconst datastr = data;\r\n\t\t\t\tconst dataObj = JSON.parse(datastr);\r\n\r\n\t\t\t\t// 忽略心跳响应\r\n\t\t\t\tif (dataObj.type === 'HEARTBEAT_RESPONSE' || dataObj.type === 'HEARTBEAT') {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n        // 处理chatId消息\r\n        if (dataObj.type === 'RETURN_YBT1_CHATID' && dataObj.chatId) {\r\n          this.userInfoReq.toneChatId = dataObj.chatId;\r\n        } else if (dataObj.type === 'RETURN_YBDS_CHATID' && dataObj.chatId) {\r\n          this.userInfoReq.ybDsChatId = dataObj.chatId;\r\n        } else if (dataObj.type === 'RETURN_DB_CHATID' && dataObj.chatId) {\r\n\t\t\t\t\t\tthis.userInfoReq.dbChatId = dataObj.chatId;\r\n        } else if (dataObj.type === \"RETURN_MAX_CHATID\" && dataObj.chatId) {\r\n          this.userInfoReq.maxChatId = dataObj.chatId;\r\n        } else if (dataObj.type === 'RETURN_TY_CHATID' && dataObj.chatId) {\r\n          this.userInfoReq.tyChatId = dataObj.chatId;\r\n        }\r\n\r\n\t\t\t\t\t// 处理进度日志消息\r\n\t\t\t\t\tif (dataObj.type === 'RETURN_PC_TASK_LOG' && dataObj.aiName) {\r\n\t\t\t\t\t\tconst targetAI = this.enabledAIs.find(ai => ai.name === dataObj.aiName);\r\n\t\t\t\t\t\tif (targetAI) {\r\n\t\t\t\t\t\t\t// 将新进度添加到数组开头\r\n\t\t\t\t\t\t\ttargetAI.progressLogs.unshift({\r\n\t\t\t\t\t\t\t\tcontent: dataObj.content,\r\n\t\t\t\t\t\t\t\ttimestamp: new Date(),\r\n\t\t\t\t\t\t\t\tisCompleted: false\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 处理截图消息\r\n\t\t\t\t\tif (dataObj.type === 'RETURN_PC_TASK_IMG' && dataObj.url) {\r\n\t\t\t\t\t\t// 将新的截图添加到数组开头\r\n\t\t\t\t\t\tthis.screenshots.unshift(dataObj.url);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 处理智能评分结果\r\n\t\t\t\t\tif (dataObj.type === 'RETURN_WKPF_RES') {\r\n\t\t\t\t\t\tconst wkpfAI = this.enabledAIs.find(ai => ai.name === '智能评分');\r\n\t\t\t\t\t\tif (wkpfAI) {\r\n\t\t\t\t\t\t\twkpfAI.status = 'completed';\r\n\t\t\t\t\t\t\tif (wkpfAI.progressLogs.length > 0) {\r\n\t\t\t\t\t\t\t\twkpfAI.progressLogs[0].isCompleted = true;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 添加评分结果到results最前面\r\n\t\t\t\t\t\t\tthis.results.unshift({\r\n\t\t\t\t\t\t\t\taiName: '智能评分',\r\n\t\t\t\t\t\t\t\tcontent: dataObj.draftContent,\r\n\t\t\t\t\t\t\t\tshareUrl: dataObj.shareUrl || '',\r\n\t\t\t\t\t\t\t\tshareImgUrl: dataObj.shareImgUrl || '',\r\n\t\t\t\t\t\t\t\ttimestamp: new Date()\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tthis.activeResultIndex = 0;\r\n\r\n\t\t\t\t\t\t\t// 折叠所有区域当智能评分完成时\r\n\t\t\t\t\t\t\tthis.sectionExpanded.aiConfig = false;\r\n\t\t\t\t\t\t\tthis.sectionExpanded.promptInput = false;\r\n\t\t\t\t\t\t\tthis.sectionExpanded.taskStatus = false;\r\n\r\n\t\t\t\t\t\t\t// 智能评分完成时，再次保存历史记录\r\n\t\t\t\t\t\t\tthis.saveHistory();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 处理智能排版结果\r\n\t\t\t\t\tif (dataObj.type === 'RETURN_ZNPB_RES') {\r\n\t\t\t\t\t\tconsole.log(\"收到智能排版结果\", dataObj);\r\n\t\t\t\t\t\tconsole.log(\"当前 currentLayoutResult:\", this.currentLayoutResult);\r\n\r\n\t\t\t\t\t\tconst znpbAI = this.enabledAIs.find(ai => ai.name === '智能排版');\r\n\t\t\t\t\t\tif (znpbAI) {\r\n\t\t\t\t\t\t\tznpbAI.status = 'completed';\r\n\t\t\t\t\t\t\tif (znpbAI.progressLogs.length > 0) {\r\n\t\t\t\t\t\t\t\tznpbAI.progressLogs[0].isCompleted = true;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t// 不添加到结果展示，直接调用推送方法\r\n\t\t\t\t\t\t\tthis.handlePushToWechat(dataObj.draftContent);\r\n\r\n\t\t\t\t\t\t\t// 智能排版完成时，保存历史记录\r\n\t\t\t\t\t\t\tthis.saveHistory();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n        // 处理知乎投递任务日志\r\n        if (dataObj.type === 'RETURN_MEDIA_TASK_LOG') {\r\n          console.log(\"收到媒体任务日志\", dataObj);\r\n          const zhihuAI = this.enabledAIs.find(ai => ai.name === dataObj.aiName);\r\n          if (zhihuAI) {\r\n            // 检查是否已存在相同内容的日志，避免重复添加\r\n            const existingLog = zhihuAI.progressLogs.find(log => log.content === dataObj.content);\r\n            if (!existingLog) {\r\n              // 添加进度日志\r\n              zhihuAI.progressLogs.push({\r\n                content: dataObj.content,\r\n                timestamp: new Date(),\r\n                isCompleted: false,\r\n                type: dataObj.aiName\r\n              });\r\n\r\n              // 强制更新UI\r\n              this.$forceUpdate();\r\n            }\r\n          }\r\n          return;\r\n        }\r\n\r\n        // 处理知乎投递完成结果\r\n        if (dataObj.type === 'RETURN_ZHIHU_DELIVERY_RES') {\r\n          console.log(\"收到知乎投递完成结果\", dataObj);\r\n          const zhihuAI = this.enabledAIs.find(ai => ai.name === '投递到知乎');\r\n          if (zhihuAI) {\r\n            zhihuAI.status = dataObj.status === 'success' ? 'completed' : 'error';\r\n\r\n            // 更新最后一条日志状态\r\n            if (zhihuAI.progressLogs.length > 0) {\r\n              zhihuAI.progressLogs[zhihuAI.progressLogs.length - 1].isCompleted = true;\r\n            }\r\n\r\n            // 添加完成日志\r\n            zhihuAI.progressLogs.push({\r\n              content: dataObj.message || '知乎投递任务完成',\r\n              timestamp: new Date(),\r\n              isCompleted: true,\r\n              type: '投递到知乎'\r\n            });\r\n\r\n            // 强制更新UI\r\n            this.$forceUpdate();\r\n\r\n            // 显示完成提示\r\n            uni.showToast({\r\n              title: dataObj.status === 'success' ? '知乎投递成功' : '知乎投递失败',\r\n              icon: dataObj.status === 'success' ? 'success' : 'failed'\r\n            });\r\n\r\n            // 保存历史记录\r\n            this.saveHistory();\r\n          }\r\n          return;\r\n        }\r\n\t\t\r\n\t\t// 处理百家号投递任务日志\r\n\t\tif (dataObj.type === 'RETURN_MEDIA_TASK_LOG') {\r\n\t\t  console.log(\"收到媒体任务日志\", dataObj);\r\n\t\t  const baijiahaoAI = this.enabledAIs.find(ai => ai.name === dataObj.aiName);\r\n\t\t  if (baijiahaoAI) {\r\n\t\t    // 检查是否已存在相同内容的日志，避免重复添加\r\n\t\t    const existingLog = baijiahaoAI.progressLogs.find(log => log.content === dataObj.content);\r\n\t\t    if (!existingLog) {\r\n\t\t      // 添加进度日志\r\n\t\t      baijiahaoAI.progressLogs.push({\r\n\t\t        content: dataObj.content,\r\n\t\t        timestamp: new Date(),\r\n\t\t        isCompleted: false,\r\n\t\t        type: dataObj.aiName\r\n\t\t      });\r\n\t\t\r\n\t\t      // 强制更新UI\r\n\t\t      this.$forceUpdate();\r\n\t\t    }\r\n\t\t  }\r\n\t\t  return;\r\n\t\t}\r\n\t\t\r\n\t\t// 处理百家号投递完成结果\r\n\t\tif (dataObj.type === 'RETURN_BAIJIAHAO_DELIVERY_RES') {\r\n\t\t  console.log(\"收到百家号投递完成结果\", dataObj);\r\n\t\t  const baijiahaoAI = this.enabledAIs.find(ai => ai.name === '投递到百家号');\r\n\t\t  if (baijiahaoAI) {\r\n\t\t    baijiahaoAI.status = dataObj.status === 'success' ? 'completed' : 'error';\r\n\t\t\r\n\t\t    // 更新最后一条日志状态\r\n\t\t    if (baijiahaoAI.progressLogs.length > 0) {\r\n\t\t      baijiahaoAI.progressLogs[baijiahaoAI.progressLogs.length - 1].isCompleted = true;\r\n\t\t    }\r\n\t\t\r\n\t\t    // 添加完成日志\r\n\t\t    baijiahaoAI.progressLogs.push({\r\n\t\t      content: dataObj.message || '百家号投递任务完成',\r\n\t\t      timestamp: new Date(),\r\n\t\t      isCompleted: true,\r\n\t\t      type: '投递到百家号'\r\n\t\t    });\r\n\t\t\r\n\t\t    // 强制更新UI\r\n\t\t    this.$forceUpdate();\r\n\t\t\r\n\t\t    // 显示完成提示\r\n\t\t    uni.showToast({\r\n\t\t      title: dataObj.status === 'success' ? '百家号投递成功' : '百家号投递失败',\r\n\t\t      icon: dataObj.status === 'success' ? 'success' : 'failed'\r\n\t\t    });\r\n\t\t\r\n\t\t    // 保存历史记录\r\n\t\t    this.saveHistory();\r\n\t\t  }\r\n\t\t  return;\r\n\t\t}\r\n\r\n        // 处理微头条排版结果\r\n        if (dataObj.type === 'RETURN_TTH_ZNPB_RES') {\r\n          // 设置微头条排版AI节点状态为completed\r\n          const tthZnpbAI = this.enabledAIs.find(ai => ai.name === '微头条排版');\r\n          if (tthZnpbAI) {\r\n            tthZnpbAI.status = 'completed';\r\n            if (tthZnpbAI.progressLogs.length > 0) {\r\n              tthZnpbAI.progressLogs[0].isCompleted = true;\r\n            }\r\n          }\r\n          this.tthArticleTitle = dataObj.title || '';\r\n          this.tthArticleContent = dataObj.content || '';\r\n          this.tthArticleEditVisible = true;\r\n          \r\n          // 确保textarea正确显示内容\r\n          this.$nextTick(() => {\r\n            // 强制更新textarea内容\r\n            const textarea = this.$el.querySelector('.score-textarea');\r\n            if (textarea) {\r\n              textarea.value = this.tthArticleContent;\r\n              // 触发input事件确保v-model同步\r\n              textarea.dispatchEvent(new Event('input', { bubbles: true }));\r\n            }\r\n          });\r\n          \r\n          if (this.saveHistory) {\r\n            this.saveHistory();\r\n          }\r\n          uni.showToast({ title: '微头条排版完成，请确认标题和内容', icon: 'success' });\r\n          return;\r\n        }\r\n\r\n        // 处理微头条发布流程\r\n        if (dataObj.type === 'RETURN_TTH_FLOW') {\r\n          if (dataObj.content) {\r\n            this.tthFlowLogs.push({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              type: 'flow'\r\n            });\r\n          }\r\n          if (dataObj.shareImgUrl) {\r\n            this.tthFlowImages.push(dataObj.shareImgUrl);\r\n          }\r\n          if (!this.tthFlowVisible) {\r\n            this.tthFlowVisible = true;\r\n          }\r\n          if (dataObj.content === 'success') {\r\n            uni.showToast({ title: '发布到微头条成功！', icon: 'success' });\r\n            this.tthFlowVisible = true;\r\n          }\r\n          if (dataObj.content === 'fail') {\r\n            uni.showToast({ title: '发布到微头条失败！', icon: 'none' });\r\n            this.tthFlowVisible = false;\r\n            this.tthArticleEditVisible = true;\r\n          }\r\n          return;\r\n        }\r\n\r\n\r\n\r\n\t\t\t\t\t// 处理AI登录状态消息\r\n\t\t\t\t\tthis.handleAiStatusMessage(datastr, dataObj);\r\n\r\n\t\t\t\t\t// 处理AI结果\r\n\t\t\t\t\tthis.handleAIResult(dataObj);\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('WebSocket消息处理错误', error);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\thandleAiStatusMessage(datastr, dataObj) {\r\n\t\t\t\t// 处理豆包登录状态\r\n\t\t\t\tif (datastr.includes(\"RETURN_DB_STATUS\") && dataObj.status != '') {\r\n\t\t\t\t\tthis.isLoading.doubao = false;\r\n\t\t\t\t\tif (!datastr.includes(\"false\")) {\r\n\t\t\t\t\t\tthis.aiLoginStatus.doubao = true;\r\n\t\t\t\t\t\tthis.accounts.doubao = dataObj.status;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.aiLoginStatus.doubao = false;\r\n\t\t\t\t\t\t// 禁用相关AI\r\n\t\t\t\t\t\tthis.disableAIsByLoginStatus('doubao');\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 更新AI启用状态\r\n\t\t\t\t\tthis.updateAiEnabledStatus();\r\n\t\t\t\t}\r\n\t\t\t\t// 处理MiniMax Chat登录状态\r\n\t\t\t\telse if (datastr.includes(\"RETURN_MAX_STATUS\") && dataObj.status != \"\") {\r\n\t\t\t\t  this.isLoading.mini = false;\r\n\t\t\t\t  if (!datastr.includes(\"false\")) {\r\n\t\t\t\t    this.aiLoginStatus.mini = true;\r\n\t\t\t\t    this.accounts.mini = dataObj.status;\r\n\t\t\t\t  } else {\r\n\t\t\t\t    this.aiLoginStatus.mini = false;\r\n\t\t\t\t    // 禁用相关AI\r\n\t\t\t\t    this.disableAIsByLoginStatus(\"mini\");\r\n\t\t\t\t  }\r\n\t\t\t\t  // 更新AI启用状态\r\n\t\t\t\t  this.updateAiEnabledStatus();\r\n\t\t\t\t}\r\n        // 处理DeepSeek登录状态\r\n        else if (datastr.includes(\"RETURN_DEEPSEEK_STATUS\")) {\r\n          console.log(\"收到DeepSeek登录状态消息:\", dataObj);\r\n          this.isLoading.deepseek = false;\r\n          if (dataObj.status && dataObj.status !== 'false' && dataObj.status !== '') {\r\n            this.aiLoginStatus.deepseek = true;\r\n            this.accounts.deepseek = dataObj.status;\r\n            console.log(\"DeepSeek登录成功，账号:\", dataObj.status);\r\n            \r\n            // 查找DeepSeek AI实例\r\n            const deepseekAI = this.aiList.find(ai => ai.name === 'DeepSeek');\r\n\r\n          } else {\r\n            this.aiLoginStatus.deepseek = false;\r\n            this.accounts.deepseek = '';\r\n            console.log(\"DeepSeek未登录\");\r\n            \r\n            // 如果未登录，确保DeepSeek被禁用\r\n            const deepseekAI = this.aiList.find(ai => ai.name === 'DeepSeek');\r\n  \r\n          }\r\n          // 强制更新UI\r\n          this.$forceUpdate();\r\n        }\r\n        else if (datastr.includes(\"RETURN_TY_STATUS\") && dataObj.status != \"\") {\r\n          this.isLoading.tongyi = false;\r\n          if (!datastr.includes(\"false\")) {\r\n            this.aiLoginStatus.tongyi = true;\r\n            this.accounts.tongyi = dataObj.status;\r\n          } else {\r\n            this.aiLoginStatus.tongyi = false;\r\n            // 禁用相关AI\r\n            this.disableAIsByLoginStatus(\"tongyi\");\r\n          }\r\n          // 更新AI启用状态\r\n          this.updateAiEnabledStatus();\r\n        }\r\n\t\t\t},\r\n\r\n\t\t\thandleAIResult(dataObj) {\r\n\t\t\t\tlet targetAI = null;\r\n\r\n\t\t\t\t// 根据消息类型匹配AI\r\n\t\t\t\tswitch (dataObj.type) {\r\n\t\t\t\t\tcase 'RETURN_DB_RES':\r\n\t\t\t\t\t\tconsole.log('收到消息:', dataObj);\r\n\t\t\t\t\t\ttargetAI = this.enabledAIs.find(ai => ai.name === '豆包');\r\n\t\t\t\t\t\tbreak;\r\n          case 'RETURN_DEEPSEEK_RES':\r\n            console.log('收到DeepSeek消息:', dataObj);\r\n            targetAI = this.enabledAIs.find(ai => ai.name === 'DeepSeek');\r\n            // 如果找不到DeepSeek，可能是因为它不在enabledAIs中，尝试添加它\r\n            if (!targetAI) {\r\n              targetAI = {\r\n                name: 'DeepSeek',\r\n                avatar: 'https://communication.cn-nb1.rains3.com/Deepseek.png',\r\n                capabilities: [{\r\n                  label: '深度思考',\r\n                  value: 'deep_thinking'\r\n                },\r\n                  {\r\n                    label: '联网搜索',\r\n                    value: 'web_search'\r\n                  }],\r\n                selectedCapabilities: ['deep_thinking', 'web_search'],\r\n                enabled: true,\r\n                status: 'running',\r\n                progressLogs: [{\r\n                  content: 'DeepSeek响应已接收',\r\n                  timestamp: new Date(),\r\n                  isCompleted: true\r\n                }],\r\n                isExpanded: true\r\n              };\r\n              this.enabledAIs.push(targetAI);\r\n            }\r\n            break;\r\n          case 'RETURN_TY_RES':\r\n            console.log('收到消息：',dataObj);\r\n            targetAI = this.enabledAIs.find(ai => ai.name === '通义千问');\r\n            break;\r\n\t\t\tcase \"RETURN_MAX_RES\":\r\n\t\t\t  console.log(\"收到消息:\", dataObj);\r\n\t\t\t  targetAI = this.enabledAIs.find((ai) => ai.name === \"MiniMax Chat\");\r\n\t\t\t  break;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (targetAI) {\r\n\t\t\t\t\t// 更新AI状态为已完成\r\n\t\t\t\t\ttargetAI.status = 'completed';\r\n\r\n\t\t\t\t\t// 将最后一条进度消息标记为已完成\r\n\t\t\t\t\tif (targetAI.progressLogs.length > 0) {\r\n\t\t\t\t\t\ttargetAI.progressLogs[0].isCompleted = true;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 添加结果到数组开头\r\n\t\t\t\t\tconst resultIndex = this.results.findIndex(r => r.aiName === targetAI.name);\r\n\t\t\t\t\tif (resultIndex === -1) {\r\n\t\t\t\t\t\tthis.results.unshift({\r\n\t\t\t\t\t\t\taiName: targetAI.name,\r\n\t\t\t\t\t\t\tcontent: dataObj.draftContent,\r\n\t\t\t\t\t\t\tshareUrl: dataObj.shareUrl || '',\r\n\t\t\t\t\t\t\tshareImgUrl: dataObj.shareImgUrl || '',\r\n\t\t\t\t\t\t\ttimestamp: new Date()\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.activeResultIndex = 0;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.results.splice(resultIndex, 1);\r\n\t\t\t\t\t\tthis.results.unshift({\r\n\t\t\t\t\t\t\taiName: targetAI.name,\r\n\t\t\t\t\t\t\tcontent: dataObj.draftContent,\r\n\t\t\t\t\t\t\tshareUrl: dataObj.shareUrl || '',\r\n\t\t\t\t\t\t\tshareImgUrl: dataObj.shareImgUrl || '',\r\n\t\t\t\t\t\t\ttimestamp: new Date()\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.activeResultIndex = 0;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 折叠所有区域当有结果返回时\r\n\t\t\t\t\tthis.sectionExpanded.aiConfig = false;\r\n\t\t\t\t\tthis.sectionExpanded.promptInput = false;\r\n\t\t\t\t\tthis.sectionExpanded.taskStatus = false;\r\n\r\n\t\t\t\t\t// 滚动到结果区域\r\n\t\t\t\t\tthis.scrollIntoView = 'results';\r\n\r\n\t\t\t\t\t// 保存历史记录\r\n\t\t\t\t\tthis.saveHistory();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 状态相关方法\r\n\t\t\tgetStatusText(status) {\r\n\t\t\t\tconst statusMap = {\r\n\t\t\t\t\t'idle': '等待中',\r\n\t\t\t\t\t'running': '正在执行',\r\n\t\t\t\t\t'completed': '已完成',\r\n\t\t\t\t\t'failed': '执行失败'\r\n\t\t\t\t};\r\n\t\t\t\treturn statusMap[status] || '未知状态';\r\n\t\t\t},\r\n\r\n\t\t\tgetStatusIconClass(status) {\r\n\t\t\t\tconst classMap = {\r\n\t\t\t\t\t'idle': 'status-idle',\r\n\t\t\t\t\t'running': 'status-running',\r\n\t\t\t\t\t'completed': 'status-completed',\r\n\t\t\t\t\t'failed': 'status-failed'\r\n\t\t\t\t};\r\n\t\t\t\treturn classMap[status] || 'status-unknown';\r\n\t\t\t},\r\n\r\n\t\t\tgetStatusEmoji(status) {\r\n\t\t\t\tconst emojiMap = {\r\n\t\t\t\t\t'idle': '⏳',\r\n\t\t\t\t\t'running': '🔄',\r\n\t\t\t\t\t'completed': '✅',\r\n\t\t\t\t\t'failed': '❌'\r\n\t\t\t\t};\r\n\t\t\t\treturn emojiMap[status] || '❓';\r\n\t\t\t},\r\n\r\n\t\t\t// 切换任务展开状态\r\n\t\t\ttoggleTaskExpansion(ai) {\r\n\t\t\t\tai.isExpanded = !ai.isExpanded;\r\n\t\t\t},\r\n\r\n\t\t\t// 切换自动播放\r\n\t\t\ttoggleAutoPlay(event) {\r\n\t\t\t\tthis.autoPlay = event.detail.value;\r\n\t\t\t},\r\n\r\n\t\t\t// 预览图片\r\n\t\t\tpreviewImage(url) {\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\tcurrent: url,\r\n\t\t\t\t\turls: [url]\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 结果相关方法\r\n\t\t\tswitchResultTab(index) {\r\n\t\t\t\tthis.activeResultIndex = index;\r\n\t\t\t},\r\n\r\n\t\t\trenderMarkdown(text) {\r\n\t\t\t\ttry {\r\n          // 对于DeepSeek响应，添加特殊的CSS类\r\n          if (this.currentResult && this.currentResult.aiName === 'DeepSeek') {\r\n            // 检查是否已经包含了deepseek-response类\r\n            if (text && text.includes('class=\"deepseek-response\"')) {\r\n              return text; // 已经包含了特殊类，直接返回\r\n            }\r\n            const renderedHtml = marked(text);\r\n            return `<div class=\"deepseek-response\">${renderedHtml}</div>`;\r\n          }\r\n\t\t\t\t\treturn marked(text);\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\treturn text;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tisImageFile(url) {\r\n\t\t\t\tif (!url) return false;\r\n\t\t\t\tconst imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];\r\n\t\t\t\tconst urlLower = url.toLowerCase();\r\n\t\t\t\treturn imageExtensions.some(ext => urlLower.includes(ext));\r\n\t\t\t},\r\n\r\n\t\t\t// 判断是否为PDF文件\r\n\t\t\tisPdfFile(url) {\r\n\t\t\t\tif (!url) return false;\r\n\t\t\t\treturn url.toLowerCase().includes('.pdf');\r\n\t\t\t},\r\n\r\n\t\t\tcopyResult(content) {\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: content,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '已复制到剪贴板',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\r\n\r\n\t\t\t// shareResult(result) {\r\n\t\t\t// \tuni.share({\r\n\t\t\t// \t\tprovider: 'weixin',\r\n\t\t\t// \t\tscene: 'WXSceneSession',\r\n\t\t\t// \t\ttype: 0,\r\n\t\t\t// \t\ttitle: `${result.aiName}的执行结果`,\r\n\t\t\t// \t\tsummary: result.content.substring(0, 100),\r\n\t\t\t// \t\tsuccess: () => {\r\n\t\t\t// \t\t\tuni.showToast({\r\n\t\t\t// \t\t\t\ttitle: '分享成功',\r\n\t\t\t// \t\t\t\ticon: 'success'\r\n\t\t\t// \t\t\t});\r\n\t\t\t// \t\t}\r\n\t\t\t// \t});\r\n\t\t\t// },\r\n\r\n\t\t\texportResult(result) {\r\n\t\t\t\t// 小程序环境下的导出功能可以通过分享或复制实现\r\n\t\t\t\tthis.copyResult(result.content);\r\n\t\t\t},\r\n\r\n\t\t\topenShareUrl(url) {\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: url,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '原链接已复制',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '复制失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 复制PDF链接\r\n\t\t\tcopyPdfUrl(url) {\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: url,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: 'PDF链接已复制',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '复制失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 打开PDF文件\r\n\t\t\topenPdfFile(url) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在下载PDF...'\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 尝试下载并打开文件\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: url,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\t\t// 打开文件\r\n\t\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: 'PDF已打开',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\t\t\t// 如果无法打开，提示并复制链接\r\n\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\t\t\t\tcontent: '无法在当前环境打开PDF文件，已复制链接到剪贴板，请在浏览器中打开',\r\n\t\t\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\t\t\t\t\tdata: url\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '下载失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t// 下载失败，提示并复制链接\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\tcontent: '下载失败，已复制PDF链接到剪贴板，请在浏览器中打开',\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\t\tdata: url\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 历史记录相关方法\r\n\t\t\tshowHistoryDrawer() {\r\n\t\t\t\tthis.historyDrawerVisible = true;\r\n\t\t\t\tthis.loadChatHistory(1);\r\n\t\t\t},\r\n\r\n\t\t\tcloseHistoryDrawer() {\r\n\t\t\t\tthis.historyDrawerVisible = false;\r\n\t\t\t},\r\n\r\n\t\t\tasync loadChatHistory(isAll) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await getChatHistory(this.userId, isAll);\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tthis.chatHistory = res.data || [];\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('加载历史记录失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '加载历史记录失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tloadHistoryItem(item) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst historyData = JSON.parse(item.data);\r\n\t\t\t\t\t// 恢复AI选择配置\r\n\t\t\t\t\tthis.aiList = historyData.aiList || this.aiList;\r\n\t\t\t\t\t// 恢复提示词输入\r\n\t\t\t\t\tthis.promptInput = historyData.promptInput || item.userPrompt;\r\n\t\t\t\t\t// 恢复任务流程\r\n\t\t\t\t\tthis.enabledAIs = historyData.enabledAIs || [];\r\n\t\t\t\t\t// 恢复主机可视化\r\n\t\t\t\t\tthis.screenshots = historyData.screenshots || [];\r\n\t\t\t\t\t// 恢复执行结果\r\n\t\t\t\t\tthis.results = historyData.results || [];\r\n\t\t\t\t\t// 恢复chatId\r\n\t\t\t\t\tthis.chatId = item.chatId || this.chatId;\r\n\t\t\t\t\tthis.userInfoReq.toneChatId = item.toneChatId || '';\r\n\t\t\t\t\tthis.userInfoReq.ybDsChatId = item.ybDsChatId || '';\r\n\t\t\t\t\tthis.userInfoReq.dbChatId = item.dbChatId || '';\r\n          this.userInfoReq.tyChatId = item.tyChatId || '';\r\n\t\t\t\t\tthis.userInfoReq.maxChatId = item.maxChatId || \"\";\r\n          this.userInfoReq.isNewChat = false;\r\n\r\n\t\t\t\t\t// 不再根据AI登录状态更新AI启用状态，保持原有选择\r\n\r\n\t\t\t\t\t// 展开相关区域\r\n\t\t\t\t\tthis.sectionExpanded.aiConfig = true;\r\n\t\t\t\t\tthis.sectionExpanded.promptInput = true;\r\n\t\t\t\t\tthis.sectionExpanded.taskStatus = true;\r\n\t\t\t\t\tthis.taskStarted = true;\r\n\r\n\t\t\t\t\tthis.closeHistoryDrawer();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '历史记录加载成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('加载历史记录失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '加载失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 加载上次会话\r\n\t\t\tasync loadLastChat() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await getChatHistory(this.userId, 0);\r\n\t\t\t\t\tif (res.code === 200 && res.data && res.data.length > 0) {\r\n\t\t\t\t\t\t// 获取最新的会话记录\r\n\t\t\t\t\t\tconst lastChat = res.data[0];\r\n\t\t\t\t\t\tthis.loadHistoryItem(lastChat);\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('加载上次会话失败:', error);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tasync saveHistory() {\r\n\t\t\t\tconst historyData = {\r\n\t\t\t\t\taiList: this.aiList,\r\n\t\t\t\t\tpromptInput: this.promptInput,\r\n\t\t\t\t\tenabledAIs: this.enabledAIs,\r\n\t\t\t\t\tscreenshots: this.screenshots,\r\n\t\t\t\t\tresults: this.results,\r\n\t\t\t\t\tchatId: this.chatId,\r\n\t\t\t\t\ttoneChatId: this.userInfoReq.toneChatId,\r\n\t\t\t\t\tybDsChatId: this.userInfoReq.ybDsChatId,\r\n\t\t\t\t\tdbChatId: this.userInfoReq.dbChatId,\r\n          tyChatId: this.userInfoReq.tyChatId,\r\n\t\t\t\t\tmaxChatId: this.userInfoReq.maxChatId,\r\n\t\t\t\t};\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait saveUserChatData({\r\n\t\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\t\tuserPrompt: this.promptInput,\r\n\t\t\t\t\t\tdata: JSON.stringify(historyData),\r\n\t\t\t\t\t\tchatId: this.chatId,\r\n\t\t\t\t\t\ttoneChatId: this.userInfoReq.toneChatId,\r\n\t\t\t\t\t\tybDsChatId: this.userInfoReq.ybDsChatId,\r\n\t\t\t\t\t\tdbChatId: this.userInfoReq.dbChatId,\r\n            tyChatId: this.userInfoReq.tyChatId,\r\n\t\t\t\t\t\tmaxChatId: this.userInfoReq.maxChatId,\r\n\t\t\t\t\t});\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('保存历史记录失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '保存历史记录失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tgetHistoryDate(timestamp) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log('getHistoryDate 输入:', timestamp, typeof timestamp);\r\n\r\n\t\t\t\t\tif (!timestamp) {\r\n\t\t\t\t\t\treturn '未知日期';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet date;\r\n\r\n\t\t\t\t\tif (typeof timestamp === 'number') {\r\n\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t} else if (typeof timestamp === 'string') {\r\n\t\t\t\t\t\t// 处理 \"2025-6-23 14:53:12\" 这种格式\r\n\t\t\t\t\t\tconst match = timestamp.match(/(\\d{4})-(\\d{1,2})-(\\d{1,2})\\s+(\\d{1,2}):(\\d{1,2}):(\\d{1,2})/);\r\n\t\t\t\t\t\tif (match) {\r\n\t\t\t\t\t\t\tconst [, year, month, day, hour, minute, second] = match;\r\n\t\t\t\t\t\t\tdate = new Date(\r\n\t\t\t\t\t\t\t\tparseInt(year),\r\n\t\t\t\t\t\t\t\tparseInt(month) - 1,\r\n\t\t\t\t\t\t\t\tparseInt(day),\r\n\t\t\t\t\t\t\t\tparseInt(hour),\r\n\t\t\t\t\t\t\t\tparseInt(minute),\r\n\t\t\t\t\t\t\t\tparseInt(second)\r\n\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 如果正则不匹配，尝试其他方式\r\n\t\t\t\t\t\t\tconst fixedTimestamp = timestamp.replace(/\\s/g, 'T');\r\n\t\t\t\t\t\t\tdate = new Date(fixedTimestamp);\r\n\r\n\t\t\t\t\t\t\tif (isNaN(date.getTime())) {\r\n\t\t\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconsole.log('getHistoryDate 解析结果:', date, date.getTime());\r\n\r\n\t\t\t\t\tif (isNaN(date.getTime())) {\r\n\t\t\t\t\t\treturn '未知日期';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconst today = new Date();\r\n\t\t\t\t\tconst yesterday = new Date(today);\r\n\t\t\t\t\tyesterday.setDate(yesterday.getDate() - 1);\r\n\r\n\t\t\t\t\tif (date.toDateString() === today.toDateString()) {\r\n\t\t\t\t\t\treturn '今天';\r\n\t\t\t\t\t} else if (date.toDateString() === yesterday.toDateString()) {\r\n\t\t\t\t\t\treturn '昨天';\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn date.toLocaleDateString('zh-CN');\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('格式化日期错误:', error, timestamp);\r\n\t\t\t\t\treturn '未知日期';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 格式化历史记录时间\r\n\t\t\tformatHistoryTime(timestamp) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log('formatHistoryTime 输入:', timestamp, typeof timestamp);\r\n\r\n\t\t\t\t\tlet date;\r\n\r\n\t\t\t\t\tif (!timestamp) {\r\n\t\t\t\t\t\treturn '时间未知';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 如果是数字，直接创建Date对象\r\n\t\t\t\t\tif (typeof timestamp === 'number') {\r\n\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t} else if (typeof timestamp === 'string') {\r\n\t\t\t\t\t\t// 处理ISO 8601格式：2025-06-25T07:18:54.110Z\r\n\t\t\t\t\t\tif (timestamp.includes('T') && (timestamp.includes('Z') || timestamp.includes('+'))) {\r\n\t\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 处理 \"2025-6-26 08:46:26\" 这种格式\r\n\t\t\t\t\t\telse {\r\n\t\t\t\t\t\t\tconst match = timestamp.match(/(\\d{4})-(\\d{1,2})-(\\d{1,2})\\s+(\\d{1,2}):(\\d{1,2}):(\\d{1,2})/);\r\n\t\t\t\t\t\t\tif (match) {\r\n\t\t\t\t\t\t\t\tconst [, year, month, day, hour, minute, second] = match;\r\n\t\t\t\t\t\t\t\t// 注意：Date构造函数的month参数是0-11，所以要减1\r\n\t\t\t\t\t\t\t\tdate = new Date(\r\n\t\t\t\t\t\t\t\t\tparseInt(year),\r\n\t\t\t\t\t\t\t\t\tparseInt(month) - 1,\r\n\t\t\t\t\t\t\t\t\tparseInt(day),\r\n\t\t\t\t\t\t\t\t\tparseInt(hour),\r\n\t\t\t\t\t\t\t\t\tparseInt(minute),\r\n\t\t\t\t\t\t\t\t\tparseInt(second)\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 如果正则不匹配，尝试其他方式\r\n\t\t\t\t\t\t\t\tconst fixedTimestamp = timestamp.replace(/\\s/g, 'T');\r\n\t\t\t\t\t\t\t\tdate = new Date(fixedTimestamp);\r\n\r\n\t\t\t\t\t\t\t\tif (isNaN(date.getTime())) {\r\n\t\t\t\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (timestamp instanceof Date) {\r\n\t\t\t\t\t\tdate = timestamp;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconsole.log('formatHistoryTime 解析结果:', date, date.getTime());\r\n\r\n\t\t\t\t\tif (isNaN(date.getTime())) {\r\n\t\t\t\t\t\treturn '时间未知';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 使用更简洁的时间格式，避免显示时区信息\r\n\t\t\t\t\tconst hour = date.getHours().toString().padStart(2, '0');\r\n\t\t\t\t\tconst minute = date.getMinutes().toString().padStart(2, '0');\r\n\r\n\t\t\t\t\tconst timeString = `${hour}:${minute}`;\r\n\r\n\t\t\t\t\tconsole.log('formatHistoryTime 输出:', timeString);\r\n\t\t\t\t\treturn timeString;\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('格式化时间错误:', error, timestamp);\r\n\t\t\t\t\treturn '时间未知';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 修改折叠切换方法\r\n\t\t\ttoggleHistoryExpansion(item) {\r\n\t\t\t\tthis.expandedHistoryItems[item.chatId] = !this.expandedHistoryItems[item.chatId];\r\n\t\t\t\tthis.$forceUpdate(); // 强制更新视图\r\n\t\t\t},\r\n\r\n\t\t\t// 智能评分相关方法\r\n\t\t\tshowScoreModal() {\r\n\t\t\t\tthis.selectedResults = [];\r\n\t\t\t\tthis.scoreModalVisible = true;\r\n\t\t\t},\r\n\r\n\t\t\tcloseScoreModal() {\r\n\t\t\t\tthis.scoreModalVisible = false;\r\n\t\t\t},\r\n\r\n\t\t\t// 媒体投递相关方法\r\n\t\t\tshowLayoutModal() {\r\n\t\t\t\tif (!this.currentResult) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '没有可投递的内容',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(\"showLayoutModal\", this.currentResult);\r\n\t\t\t\t// 深度拷贝当前结果，避免引用被修改\r\n\t\t\t\tthis.currentLayoutResult = {\r\n\t\t\t\t\taiName: this.currentResult.aiName,\r\n\t\t\t\t\tcontent: this.currentResult.content,\r\n\t\t\t\t\tshareUrl: this.currentResult.shareUrl,\r\n\t\t\t\t\tshareImgUrl: this.currentResult.shareImgUrl,\r\n\t\t\t\t\ttimestamp: this.currentResult.timestamp\r\n\t\t\t\t};\r\n\r\n        // 默认选择公众号\r\n        this.selectedMedia = 'wechat';\r\n        // 加载对应媒体的提示词\r\n        this.loadMediaPrompt(this.selectedMedia);\r\n        this.layoutModalVisible = true;\r\n      },\r\n      // 选择媒体\r\n      selectMedia(media) {\r\n        this.selectedMedia = media;\r\n        this.loadMediaPrompt(media);\r\n      },\r\n\r\n// 加载媒体提示词\r\n      async loadMediaPrompt(media) {\r\n        try {\r\n          let platformId;\r\n\t\t  if(media === 'wechat'){\r\n\t\t\t  platformId = 'wechat_layout'\r\n\t\t  }else if(media === 'zhihu'){\r\n\t\t\t  platformId = 'zhihu_layout'\r\n\t\t  }else if(media === 'baijiahao'){\r\n\t\t\t  platformId = 'baijiahao_layout'\r\n\t\t  }else if(media === 'toutiao'){\r\n\t\t\t  platformId = 'weitoutiao_layout'\r\n\t\t  }\r\n          const res = await getMediaCallWord(platformId);\r\n          if (res.code === 200) {\r\n            this.layoutPrompt = res.data;\r\n          } else {\r\n            this.layoutPrompt = this.getDefaultPrompt(media);\r\n          }\r\n        } catch (error) {\r\n          console.error('加载提示词失败:', error);\r\n          this.layoutPrompt = this.getDefaultPrompt(media);\r\n        }\r\n      },\r\n\r\n      // 获取默认提示词（仅在后端获取失败时作为备选）\r\n      getDefaultPrompt(media) {\r\n        if (media === 'wechat') {\r\n          return `请你对以下 HTML 内容进行排版优化，目标是用于微信公众号\"草稿箱接口\"的 content 字段，要求如下：\r\n\r\n1. 仅返回 <body> 内部可用的 HTML 内容片段（不要包含 <!DOCTYPE>、<html>、<head>、<meta>、<title> 等标签）。\r\n2. 所有样式必须以\"内联 style\"方式写入。\r\n3. 保持结构清晰、视觉友好，适配公众号图文排版。\r\n4. 请直接输出代码，不要添加任何注释或额外说明。\r\n5. 不得使用 emoji 表情符号或小图标字符。\r\n6. 不要显示为问答形式，以一篇文章的格式去调整\r\n\r\n以下为需要进行排版优化的内容：\r\n`;\r\n        } else if (media === 'zhihu'){\r\n          return `请将以下内容转换为适合知乎平台的专业文章格式，要求：\r\n\r\n1. 保持内容的逻辑性和可读性\r\n2. 适当使用Markdown格式（标题、列表、代码块等）\r\n3. 确保在知乎平台上显示效果良好\r\n4. 保持专业性和吸引力\r\n5. 直接输出转换后的内容，无需额外说明\r\n\r\n以下为需要转换的内容：\r\n`;\r\n        }else if (media === 'toutiao'){\r\n\t\t          return `请将以下内容转换为适合微头条平台的文章格式，要求：\r\n\r\n1. 标题要简洁明了，吸引人\r\n2. 内容要结构清晰，易于阅读\r\n3. 不要包含任何HTML标签\r\n4. 直接输出纯文本格式\r\n5. 内容要适合微头条发布\r\n6. 字数控制在1000-2000字之间\r\n7. 保持内容的专业性和可读性\r\n8. 直接输出转换后的内容，无需额外说明\r\n\r\n以下为需要转换的内容：\r\n\t\t`;\r\n\t\t        }else if (media === 'baijiahao'){\r\n\t\t          return `请将以下内容整理为适合百家号发布的纯文本格式文章。\r\n要求：\r\n1.（不要使用Markdown或HTML语法，仅使用普通文本和简单换行保持内容的专业性和可读性使用自然段落分隔，）\r\n2.不允许使用有序列表，包括“一、”，“1.”等的序列号。\r\n3.给文章取一个吸引人的标题，放在正文的第一段\r\n4.不允许出现代码框、数学公式、表格或其他复杂格式删除所有Markdown和HTML标签，\r\n5.只保留纯文本内容\r\n6.目标是作为一篇专业文章投递到百家号草稿箱\r\n7.直接以文章标题开始，以文章末尾结束，不允许添加其他对话\r\n\t\t`;\r\n\t\t        }\r\n\t\t\t},\r\n\r\n\t\t\tcloseLayoutModal() {\r\n\t\t\t\tthis.layoutModalVisible = false;\r\n\t\t\t},\r\n\r\n      handleLayout() {\r\n        if (this.layoutPrompt.trim().length === 0) return;\r\n\r\n        this.closeLayoutModal();\r\n\r\n        if (this.selectedMedia === 'zhihu') {\r\n          this.createZhihuDeliveryTask();\r\n        } else if (this.selectedMedia === 'toutiao') {\r\n          this.createToutiaoLayoutTask();\r\n        } else if (this.selectedMedia === 'baijiahao') {\r\n          this.createBaijiahaoDeliveryTask();\r\n        } else {\r\n          this.createWechatLayoutTask();\r\n        }\r\n      },\r\n\r\n      // 创建知乎投递任务\r\n      createZhihuDeliveryTask() {\r\n        // 组合完整的提示词：数据库提示词 + 原文内容\r\n        const fullPrompt = this.layoutPrompt + '\\n\\n' + this.currentLayoutResult.content;\r\n\r\n        // 构建知乎投递请求\r\n        const zhihuRequest = {\r\n          jsonrpc: '2.0',\r\n          id: this.generateUUID(),\r\n          method: '投递到知乎',\r\n          params: {\r\n            taskId: this.generateUUID(),\r\n            userId: this.userId,\r\n            corpId: this.corpId,\r\n            userPrompt: fullPrompt,\r\n            aiName: this.currentLayoutResult.aiName,\r\n            content: this.currentLayoutResult.content\r\n          }\r\n        };\r\n\r\n        console.log(\"知乎投递参数\", zhihuRequest);\r\n        this.message(zhihuRequest);\r\n\r\n        // 创建投递到知乎任务节点\r\n        const zhihuAI = {\r\n          name: '投递到知乎',\r\n          avatar: 'https://pic1.zhimg.com/80/v2-a47051e92cf74930bedd7469978e6c08_720w.png',\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'running',\r\n          progressLogs: [\r\n            {\r\n              content: '投递到知乎任务已提交，正在处理...',\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n              type: '投递到知乎'\r\n            }\r\n          ],\r\n          isExpanded: true\r\n        };\r\n\r\n        this.addOrUpdateTaskAI(zhihuAI, '投递到知乎');\r\n\r\n        uni.showToast({\r\n          title: '知乎投递任务已提交',\r\n          icon: 'success'\r\n        });\r\n      },\r\n\t  \r\n\t  // 创建百家号投递任务\r\n\t  createBaijiahaoDeliveryTask() {\r\n\t    // 组合完整的提示词：数据库提示词 + 原文内容\r\n\t    const fullPrompt = this.layoutPrompt + '\\n\\n' + this.currentLayoutResult.content;\r\n\t  \r\n\t    // 构建百家号投递请求\r\n\t    const baijiahaoRequest = {\r\n\t      jsonrpc: '2.0',\r\n\t      id: this.generateUUID(),\r\n\t      method: '投递到百家号',\r\n\t      params: {\r\n\t        taskId: this.generateUUID(),\r\n\t        userId: this.userId,\r\n\t        corpId: this.corpId,\r\n\t        userPrompt: fullPrompt,\r\n\t        aiName: this.currentLayoutResult.aiName,\r\n\t        content: this.currentLayoutResult.content\r\n\t      }\r\n\t    };\r\n\t  \r\n\t    console.log(\"百家号投递参数\", baijiahaoRequest);\r\n\t    this.message(baijiahaoRequest);\r\n\t  \r\n\t    // 创建投递到百家号任务节点\r\n\t    const baijiahaoAI = {\r\n\t      name: '投递到百家号',\r\n\t      avatar: 'https://my-image-hosting.oss-cn-beijing.aliyuncs.com/baojiahao.png',\r\n\t      capabilities: [],\r\n\t      selectedCapabilities: [],\r\n\t      enabled: true,\r\n\t      status: 'running',\r\n\t      progressLogs: [\r\n\t        {\r\n\t          content: '投递到百家号任务已提交，正在处理...',\r\n\t          timestamp: new Date(),\r\n\t          isCompleted: false,\r\n\t          type: '投递到百家号'\r\n\t        }\r\n\t      ],\r\n\t      isExpanded: true\r\n\t    };\r\n\t  \r\n\t    this.addOrUpdateTaskAI(baijiahaoAI, '投递到百家号');\r\n\t  \r\n\t    uni.showToast({\r\n\t      title: '百家号投递任务已提交',\r\n\t      icon: 'success'\r\n\t    });\r\n\t  },\r\n\t  \r\n\r\n      // 创建微头条排版任务\r\n      createToutiaoLayoutTask() {\r\n        // 组合完整的提示词：数据库提示词 + 原文内容\r\n        const fullPrompt = this.layoutPrompt + '\\n\\n' + this.currentLayoutResult.content;\r\n\r\n        // 构建微头条排版请求\r\n        const layoutRequest = {\r\n          jsonrpc: '2.0',\r\n          id: this.generateUUID(),\r\n          method: '微头条排版',\r\n          params: {\r\n            taskId: this.generateUUID(),\r\n            userId: this.userId,\r\n            corpId: this.corpId,\r\n            userPrompt: fullPrompt,\r\n            roles: ''\r\n          }\r\n        };\r\n\r\n        console.log(\"微头条排版参数\", layoutRequest);\r\n        this.message(layoutRequest);\r\n\r\n        // 创建微头条排版AI节点\r\n        const tthZnpbAI = {\r\n          name: '微头条排版',\r\n          avatar: 'https://u3w.com/chatfile/TouTiao.png',\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'running',\r\n          progressLogs: [\r\n            {\r\n              content: '微头条排版任务已提交，正在排版...',\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n              type: '微头条排版'\r\n            }\r\n          ],\r\n          isExpanded: true\r\n        };\r\n\r\n        this.addOrUpdateTaskAI(tthZnpbAI, '微头条排版');\r\n\r\n        uni.showToast({\r\n          title: '微头条排版任务已提交',\r\n          icon: 'success'\r\n        });\r\n      },\r\n\r\n      // 创建公众号排版任务\r\n        createWechatLayoutTask() {\r\n          // 组合完整的提示词：数据库提示词 + 原文内容\r\n          const fullPrompt = this.layoutPrompt + '\\n\\n' + this.currentLayoutResult.content;\r\n\r\n          // 构建智能排版请求\r\n\t\t\t\tconst layoutRequest = {\r\n\t\t\t\t\tjsonrpc: '2.0',\r\n\t\t\t\t\tid: this.generateUUID(),\r\n\t\t\t\t\tmethod: 'AI排版',\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\ttaskId: this.generateUUID(),\r\n\t\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\t\tcorpId: this.corpId,\r\n\t\t\t\t\t\tuserPrompt: fullPrompt,\r\n\t\t\t\t\t\troles: 'zj-db-sdsk' // 默认使用豆包进行排版\r\n\t\t\t\t\t}\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 发送排版请求\r\n\t\t\t\tconsole.log(\"智能排版参数\", layoutRequest);\r\n\t\t\t\tthis.message(layoutRequest);\r\n\t\t\t\t// this.closeLayoutModal();\r\n\r\n\t\t\t\t// 创建智能排版AI节点\r\n\t\t\t\tconst znpbAI = {\r\n\t\t\t\t\tname: '智能排版',\r\n\t\t\t\t\tavatar: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\r\n\t\t\t\t\tcapabilities: [],\r\n\t\t\t\t\tselectedCapabilities: [],\r\n\t\t\t\t\tenabled: true,\r\n\t\t\t\t\tstatus: 'running',\r\n\t\t\t\t\tprogressLogs: [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tcontent: '智能排版任务已提交，正在排版...',\r\n\t\t\t\t\t\t\ttimestamp: new Date(),\r\n\t\t\t\t\t\t\tisCompleted: false,\r\n\t\t\t\t\t\t\ttype: '智能排版'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t],\r\n\t\t\t\t\tisExpanded: true\r\n\t\t\t\t};\r\n          this.addOrUpdateTaskAI(znpbAI, '智能排版');\r\n\r\n          uni.showToast({\r\n            title: '排版请求已发送，请等待结果',\r\n            icon: 'success'\r\n          });\r\n        },\r\n\r\n        // 添加或更新任务AI\r\n        addOrUpdateTaskAI(aiNode, taskName) {\r\n          const existIndex = this.enabledAIs.findIndex(ai => ai.name === taskName);\r\n          if (existIndex === -1) {\r\n            // 如果不存在，添加到数组开头\r\n            this.enabledAIs.unshift(aiNode);\r\n          } else {\r\n            // 如果已存在，更新状态和日志\r\n            this.enabledAIs[existIndex] = aiNode;\r\n            // 将任务移到数组开头\r\n            const task = this.enabledAIs.splice(existIndex, 1)[0];\r\n            this.enabledAIs.unshift(task);\r\n          }\r\n        },\r\n\r\n\r\n\t\t\t// 推送到公众号\r\n\t\t\tasync handlePushToWechat(contentText) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log(\"handlePushToWechat 开始执行\", this.currentLayoutResult);\r\n\r\n\t\t\t\t\tif (!this.currentLayoutResult) {\r\n\t\t\t\t\t\tconsole.error(\"currentLayoutResult 为空，无法投递\");\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '投递失败：缺少原始结果信息',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '正在投递...'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 自增计数器\r\n\t\t\t\t\tthis.collectNum++;\r\n\r\n\t\t\t\t\tconst params = {\r\n\t\t\t\t\t\tcontentText: contentText,\r\n\t\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\t\tshareUrl: this.currentLayoutResult.shareUrl || '',\r\n\t\t\t\t\t\taiName: this.currentLayoutResult.aiName || '',\r\n\t\t\t\t\t\tnum: this.collectNum\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.log(\"投递参数\", params);\r\n\r\n\t\t\t\t\tconst res = await pushAutoOffice(params);\r\n\r\n\t\t\t\t\tuni.hideLoading();\r\n\r\n\t\t\t\t\tif (res.code === 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: `投递成功(${this.collectNum})`,\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.message || '投递失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tconsole.error('投递到公众号失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '投递失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\ttoggleResultSelection(event) {\r\n\t\t\t\tconst values = event.detail.value;\r\n\t\t\t\tconsole.log('toggleResultSelection - 选中的values:', values);\r\n\t\t\t\tconsole.log('toggleResultSelection - 当前scorePrompt:', this.scorePrompt.trim());\r\n\t\t\t\tthis.selectedResults = values;\r\n\t\t\t\tconsole.log('toggleResultSelection - 更新后的selectedResults:', this.selectedResults);\r\n\t\t\t\tconsole.log('toggleResultSelection - canScore状态:', this.canScore);\r\n\t\t\t},\r\n\r\n\t\t\thandleScore() {\r\n\t\t\t\tif (!this.canScore) return;\r\n\r\n\t\t\t\t// 获取选中的结果内容并按照指定格式拼接\r\n\t\t\t\tconst selectedContents = this.results\r\n\t\t\t\t\t.filter(result => this.selectedResults.includes(result.aiName))\r\n\t\t\t\t\t.map(result => {\r\n\t\t\t\t\t\t// 将HTML内容转换为纯文本（小程序版本简化处理）\r\n\t\t\t\t\t\tconst plainContent = result.content.replace(/<[^>]*>/g, '');\r\n\t\t\t\t\t\treturn `${result.aiName}初稿：\\n${plainContent}\\n`;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.join('\\n');\r\n\r\n\t\t\t\t// 构建完整的评分提示内容\r\n\t\t\t\tconst fullPrompt = `${this.scorePrompt}\\n${selectedContents}`;\r\n\r\n\t\t\t\t// 构建评分请求\r\n\t\t\t\tconst scoreRequest = {\r\n\t\t\t\t\tjsonrpc: '2.0',\r\n\t\t\t\t\tid: this.generateUUID(),\r\n\t\t\t\t\tmethod: 'AI评分',\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\ttaskId: this.generateUUID(),\r\n\t\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\t\tcorpId: this.corpId,\r\n\t\t\t\t\t\tuserPrompt: fullPrompt,\r\n\t\t\t\t\t\troles: 'zj-db-sdsk' // 默认使用豆包进行评分\r\n\t\t\t\t\t}\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 发送评分请求\r\n\t\t\t\tconsole.log(\"参数\", scoreRequest);\r\n\t\t\t\tthis.message(scoreRequest);\r\n\t\t\t\tthis.closeScoreModal();\r\n\r\n\t\t\t\t// 创建智能评分AI节点\r\n\t\t\t\tconst wkpfAI = {\r\n\t\t\t\t\tname: '智能评分',\r\n\t\t\t\t\tavatar: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\r\n\t\t\t\t\tcapabilities: [],\r\n\t\t\t\t\tselectedCapabilities: [],\r\n\t\t\t\t\tenabled: true,\r\n\t\t\t\t\tstatus: 'running',\r\n\t\t\t\t\tprogressLogs: [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tcontent: '智能评分任务已提交，正在评分...',\r\n\t\t\t\t\t\t\ttimestamp: new Date(),\r\n\t\t\t\t\t\t\tisCompleted: false,\r\n\t\t\t\t\t\t\ttype: '智能评分'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t],\r\n\t\t\t\t\tisExpanded: true\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 检查是否已存在智能评分\r\n\t\t\t\tconst existIndex = this.enabledAIs.findIndex(ai => ai.name === '智能评分');\r\n\t\t\t\tif (existIndex === -1) {\r\n\t\t\t\t\t// 如果不存在，添加到数组开头\r\n\t\t\t\t\tthis.enabledAIs.unshift(wkpfAI);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果已存在，更新状态和日志\r\n\t\t\t\t\tthis.enabledAIs[existIndex] = wkpfAI;\r\n\t\t\t\t\t// 将智能评分移到数组开头\r\n\t\t\t\t\tconst wkpf = this.enabledAIs.splice(existIndex, 1)[0];\r\n\t\t\t\t\tthis.enabledAIs.unshift(wkpf);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '评分请求已发送，请等待结果',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 创建新对话\r\n\t\t\tcreateNewChat() {\r\n\t\t\t\t// 重置所有数据\r\n\t\t\t\tthis.chatId = this.generateUUID();\r\n\t\t\t\tthis.promptInput = '';\r\n\t\t\t\tthis.taskStarted = false;\r\n\t\t\t\tthis.screenshots = [];\r\n\t\t\t\tthis.results = [];\r\n\t\t\t\tthis.enabledAIs = [];\r\n\t\t\t\tthis.userInfoReq = {\r\n\t\t\t\t\tuserPrompt: '',\r\n\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\tcorpId: this.corpId,\r\n\t\t\t\t\ttaskId: '',\r\n\t\t\t\t\troles: '',\r\n\t\t\t\t\ttoneChatId: '',\r\n\t\t\t\t\tybDsChatId: '',\r\n\t\t\t\t\tdbChatId: '',\r\n          tyChatId: '',\r\n\t\t\t\t\tmaxChatId: '',\r\n\t\t\t\t\tisNewChat: true\r\n\t\t\t\t};\r\n\t\t\t\t// 重置AI列表为初始状态\r\n\t\t\t\tthis.aiList = [{\r\n            name: 'DeepSeek',\r\n            avatar: 'https://communication.cn-nb1.rains3.com/Deepseek.png',\r\n            capabilities: [{\r\n              label: '深度思考',\r\n              value: 'deep_thinking'\r\n            },\r\n              {\r\n                label: '联网搜索',\r\n                value: 'web_search'\r\n              }\r\n            ],\r\n            selectedCapabilities: ['deep_thinking', 'web_search'],\r\n            enabled: true,\r\n            status: 'idle',\r\n            progressLogs: [],\r\n            isExpanded: true\r\n          },\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '豆包',\r\n\t\t\t\t\t\tavatar: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\r\n\t\t\t\t\t\tcapabilities: [{\r\n\t\t\t\t\t\t\tlabel: '深度思考',\r\n\t\t\t\t\t\t\tvalue: 'deep_thinking'\r\n\t\t\t\t\t\t}],\r\n\t\t\t\t\t\tselectedCapabilities: ['deep_thinking'],\r\n\t\t\t\t\t\tenabled: true,\r\n\t\t\t\t\t\tstatus: 'idle',\r\n\t\t\t\t\t\tprogressLogs: [],\r\n\t\t\t\t\t\tisExpanded: true\r\n\t\t\t\t\t},\r\n          {\r\n            name: '通义千问',\r\n            avatar: 'https://u3w.com/chatfile/TongYi.png',\r\n            capabilities: [\r\n              { label: '深度思考', value: 'deep_thinking' },\r\n              { label: '联网搜索', value: 'web_search' }\r\n            ],\r\n            selectedCapability: '',\r\n            enabled: true,\r\n            status: 'idle',\r\n            progressLogs: [],\r\n            isExpanded: true\r\n          },\r\n\t\t\t\t\t{\r\n\t\t\t\t\t  name: \"MiniMax Chat\",\r\n\t\t\t\t\t  avatar:\r\n\t\t\t\t\t    \"https://u3w.com/chatfile/MiniMaxChat.png\",\r\n\t\t\t\t\t  capabilities: [\r\n\t\t\t\t\t    { label: \"深度思考\", value: \"deep_thinking\" },\r\n\t\t\t\t\t    { label: \"联网搜索\", value: \"web_search\" },\r\n\t\t\t\t\t  ],\r\n\t\t\t\t\t  selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n\t\t\t\t\t  enabled: true,\r\n\t\t\t\t\t  status: \"idle\",\r\n\t\t\t\t\t  progressLogs: [],\r\n\t\t\t\t\t  isExpanded: true,\r\n\t\t\t\t\t},\r\n\t\t\t\t];\r\n\t\t\t\t// 不再根据AI登录状态更新AI启用状态，保持原有选择\r\n\r\n\t\t\t\t// 展开相关区域\r\n\t\t\t\tthis.sectionExpanded.aiConfig = true;\r\n\t\t\t\tthis.sectionExpanded.promptInput = true;\r\n\t\t\t\tthis.sectionExpanded.taskStatus = true;\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '已创建新对话',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// AI状态相关方法\r\n\t\t\tcheckAiLoginStatus() {\r\n\t\t\t\t// 延迟检查，确保WebSocket连接已建立\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.sendAiStatusCheck();\r\n\t\t\t\t\t// 不再更新AI启用状态，保持原有选择\r\n\t\t\t\t}, 2000);\r\n\t\t\t},\r\n\r\n\t\t\tsendAiStatusCheck() {\r\n\t\t\t\t// 检查豆包登录状态\r\n\t\t\t\tthis.sendWebSocketMessage({\r\n\t\t\t\t\ttype: 'PLAY_CHECK_DB_LOGIN',\r\n\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\tcorpId: this.corpId\r\n\t\t\t\t});\r\n\r\n        // 检查DeepSeek登录状态\r\n        this.sendWebSocketMessage({\r\n          type: 'PLAY_CHECK_DEEPSEEK_LOGIN',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        });\r\n\r\n        // 检查通义千问登录状态\r\n        this.sendWebSocketMessage({\r\n          type: 'PLAY_CHECK_QW_LOGIN',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        })\r\n\r\n        // 检查MiniMax登录状态\r\n        this.sendWebSocketMessage({\r\n          type: \"PLAY_CHECK_MAX_LOGIN\",\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n        });\r\n\t\t\t},\r\n\r\n\t\t\tgetPlatformIcon(type) {\r\n\t\t\t\tconst icons = {\r\n\t\t\t\t\tyuanbao: 'https://u3w.com/chatfile/yuanbao.png',\r\n\t\t\t\t\tdoubao: 'https://u3w.com/chatfile/%E8%B1%86%E5%8C%85.png',\r\n\t\t\t\t\tagent: 'https://u3w.com/chatfile/yuanbao.png',\r\n          tongyi: 'https://u3w.com/chatfile/TongYi.png',\r\n\t\t\t\t};\r\n\t\t\t\treturn icons[type] || '';\r\n\t\t\t},\r\n\r\n\t\t\tgetPlatformName(type) {\r\n\t\t\t\tconst names = {\r\n\t\t\t\t\tyuanbao: '腾讯元宝',\r\n\t\t\t\t\tdoubao: '豆包',\r\n\t\t\t\t\tagent: '智能体',\r\n          tongyi: '通义千问',\r\n\t\t\t\t};\r\n\t\t\t\treturn names[type] || '';\r\n\t\t\t},\r\n\r\n\r\n\r\n\r\n\r\n\t\t\trefreshAiStatus() {\r\n\t\t\t\t// 重置所有AI状态为加载中\r\n\t\t\t\tthis.isLoading = {\r\n\t\t\t\t\tdoubao: true,\r\n          deepseek: true,\r\n          tongyi: true,\r\n\t\t      mini: true,\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 重置登录状态\r\n\t\t\t\tthis.aiLoginStatus = {\r\n\t\t\t\t\tdoubao: false,\r\n          deepseek: false,\r\n\t\t      mini: false,\r\n          tongyi: false,\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 重置账户信息\r\n\t\t\t\tthis.accounts = {\r\n\t\t\t\t\tdoubao: '',\r\n          deepseek: '',\r\n          tongyi: '',\r\n\t\t      mini: '',\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 显示刷新提示\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '正在刷新连接状态...',\r\n\t\t\t\t\ticon: 'loading',\r\n\t\t\t\t\tduration: 1500\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 重新建立WebSocket连接\r\n\t\t\t\tthis.closeWebSocket();\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.initWebSocket();\r\n\t\t\t\t\t// 延迟检查AI状态，确保WebSocket重新连接\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.sendAiStatusCheck();\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\r\n\t\t\t// 判断AI是否已登录可用\r\n\t\t\tisAiLoginEnabled(ai) {\r\n\t\t\t\tswitch (ai.name) {\r\n\t\t\t\t\tcase '豆包':\r\n\t\t\t\t\t\treturn this.aiLoginStatus.doubao; // 豆包登录状态\r\n          case 'DeepSeek':\r\n            return this.aiLoginStatus.deepseek; // 使用实际的DeepSeek登录状态\r\n          case '通义千问':\r\n            return this.aiLoginStatus.tongyi;   // 通义登录状态\r\n          case \"MiniMax Chat\":\r\n            return this.aiLoginStatus.mini; // MiniMax Chat登录状态\r\n          default:\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 判断AI是否在加载状态\r\n\t\t\tisAiInLoading(ai) {\r\n\t\t\t\tswitch (ai.name) {\r\n\t\t\t\t\tcase '豆包':\r\n\t\t\t\t\t\treturn this.isLoading.doubao;\r\n          case 'DeepSeek':\r\n            return this.isLoading.deepseek; // 使用实际的DeepSeek加载状态\r\n          case '通义千问':\r\n            return this.isLoading.tongyi;\r\n          case \"MiniMax Chat\":\r\n            return this.isLoading.mini;\r\n          default:\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 根据登录状态禁用相关AI（已废弃，不再修改enabled状态）\r\n\t\t\tdisableAIsByLoginStatus(loginType) {\r\n\t\t\t\t// 不再修改enabled状态，只通过UI控制操作权限\r\n\t\t\t\tconsole.log(`AI ${loginType} 登录状态已更新，但保持原有选择`);\r\n\t\t\t},\r\n\r\n\t\t\t// 根据当前AI登录状态更新AI启用状态（已废弃，不再修改enabled状态）\r\n\t\t\tupdateAiEnabledStatus() {\r\n\t\t\t\t// 不再修改enabled状态，只通过UI控制操作权限\r\n\t\t\t\tconsole.log('AI登录状态已更新，但保持原有选择');\r\n\t\t\t},\r\n\r\n\t\t\t// 微头条相关方法\r\n\t\t\t// 微头条文章编辑相关方法\r\n\t\t\tshowTthArticleEditModal() {\r\n\t\t\t\tthis.tthArticleEditVisible = true;\r\n\t\t\t},\r\n\r\n\t\t\tcloseTthArticleEditModal() {\r\n\t\t\t\tthis.tthArticleEditVisible = false;\r\n\t\t\t},\r\n\r\n\t\t\tconfirmTTHPublish() {\r\n\t\t\t\tif (!this.tthArticleTitle || !this.tthArticleContent) {\r\n\t\t\t\t\tuni.showToast({ title: '请填写标题和内容', icon: 'none' });\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tconst publishRequest = {\r\n\t\t\t\t\tjsonrpc: '2.0',\r\n\t\t\t\t\tid: this.generateUUID(),\r\n\t\t\t\t\tmethod: '微头条发布',\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\ttaskId: this.generateUUID(),\r\n\t\t\t\t\t\tuserId: this.userId,\r\n\t\t\t\t\t\tcorpId: this.corpId,\r\n\t\t\t\t\t\troles: '',\r\n\t\t\t\t\t\ttitle: this.tthArticleTitle,\r\n\t\t\t\t\t\tcontent: this.tthArticleContent,\r\n\t\t\t\t\t\ttype: '微头条发布'\r\n\t\t\t\t\t}\r\n\t\t\t\t};\r\n\t\t\t\tthis.message(publishRequest);\r\n\t\t\t\tthis.tthArticleEditVisible = false;\r\n\t\t\t\tthis.tthFlowVisible = true;\r\n\t\t\t\tthis.tthFlowLogs = [];\r\n\t\t\t\tthis.tthFlowImages = [];\r\n\t\t\t\tuni.showToast({ title: '微头条发布请求已发送！', icon: 'success' });\r\n\t\t\t},\r\n\r\n\r\n\r\n\t\t\t// 微头条发布流程相关方法\r\n\t\t\tcloseTthFlowDialog() {\r\n\t\t\t\tthis.tthFlowVisible = false;\r\n\t\t\t\tthis.tthFlowLogs = [];\r\n\t\t\t\tthis.tthFlowImages = [];\r\n\t\t\t},\r\n\r\n\t\t\t// HTML转纯文本方法\r\n\t\t\thtmlToText(html) {\r\n\t\t\t\tif (!html) return '';\r\n\t\t\t\treturn html.replace(/<[^>]*>/g, '');\r\n\t\t\t},\r\n\r\n\t\t\t// 格式化时间\r\n\t\t\tformatTime(timestamp) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tif (!timestamp) {\r\n\t\t\t\t\t\treturn '时间未知';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet date;\r\n\r\n\t\t\t\t\tif (typeof timestamp === 'number') {\r\n\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t} else if (typeof timestamp === 'string') {\r\n\t\t\t\t\t\t// 处理ISO 8601格式：2025-06-25T07:18:54.110Z\r\n\t\t\t\t\t\tif (timestamp.includes('T') && (timestamp.includes('Z') || timestamp.includes('+'))) {\r\n\t\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 处理 \"2025-6-23 14:53:12\" 这种格式\r\n\t\t\t\t\t\telse {\r\n\t\t\t\t\t\t\tconst match = timestamp.match(/(\\d{4})-(\\d{1,2})-(\\d{1,2})\\s+(\\d{1,2}):(\\d{1,2}):(\\d{1,2})/);\r\n\t\t\t\t\t\t\tif (match) {\r\n\t\t\t\t\t\t\t\tconst [, year, month, day, hour, minute, second] = match;\r\n\t\t\t\t\t\t\t\tdate = new Date(\r\n\t\t\t\t\t\t\t\t\tparseInt(year),\r\n\t\t\t\t\t\t\t\t\tparseInt(month) - 1,\r\n\t\t\t\t\t\t\t\t\tparseInt(day),\r\n\t\t\t\t\t\t\t\t\tparseInt(hour),\r\n\t\t\t\t\t\t\t\t\tparseInt(minute),\r\n\t\t\t\t\t\t\t\t\tparseInt(second)\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 如果正则不匹配，尝试其他方式\r\n\t\t\t\t\t\t\t\tconst fixedTimestamp = timestamp.replace(/\\s/g, 'T');\r\n\t\t\t\t\t\t\t\tdate = new Date(fixedTimestamp);\r\n\r\n\t\t\t\t\t\t\t\tif (isNaN(date.getTime())) {\r\n\t\t\t\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (timestamp instanceof Date) {\r\n\t\t\t\t\t\tdate = timestamp;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tdate = new Date(timestamp);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (isNaN(date.getTime())) {\r\n\t\t\t\t\t\treturn '时间未知';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 使用更简洁的时间格式，避免显示时区信息\r\n\t\t\t\t\tconst hour = date.getHours().toString().padStart(2, '0');\r\n\t\t\t\t\tconst minute = date.getMinutes().toString().padStart(2, '0');\r\n\t\t\t\t\tconst second = date.getSeconds().toString().padStart(2, '0');\r\n\r\n\t\t\t\t\tconst timeString = `${hour}:${minute}:${second}`;\r\n\r\n\t\t\t\t\treturn timeString;\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('格式化时间错误:', error, timestamp);\r\n\t\t\t\t\treturn '时间未知';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped>\r\n\t.console-container {\r\n\t\theight: 100vh;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* 顶部固定区域 */\r\n\t.header-fixed {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tz-index: 1000;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t}\r\n\r\n\t.header-content {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 10px 15px;\r\n\t\tpadding-top: calc(10px + var(--status-bar-height));\r\n\t}\r\n\r\n\t.header-title {\r\n\t\tfont-size: 18px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\t.header-actions {\r\n\t\tdisplay: flex;\r\n\t\tgap: 10px;\r\n\t}\r\n\r\n\t.action-btn {\r\n\t\twidth: 36px;\r\n\t\theight: 36px;\r\n\t\tborder-radius: 18px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.action-btn:active {\r\n\t\ttransform: scale(0.92);\r\n\t\topacity: 0.7;\r\n\t}\r\n  .connection-indicator {\r\n    position: absolute;\r\n    top: -2px;\r\n    right: -2px;\r\n    width: 8px;\r\n    height: 8px;\r\n    border-radius: 50%;\r\n    border: 1px solid #fff;\r\n    z-index: 1;\r\n  }\r\n\r\n  .connection-indicator.connected {\r\n    background-color: #52c41a;\r\n    box-shadow: 0 0 4px rgba(82, 196, 26, 0.6);\r\n  }\r\n\r\n  .connection-indicator.disconnected {\r\n    background-color: #ff4d4f;\r\n    box-shadow: 0 0 4px rgba(255, 77, 79, 0.6);\r\n  }\r\n\r\n\t.action-icon {\r\n\t\tfont-size: 18px;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: 500;\r\n\t\ttext-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 1;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.action-icon-img {\r\n\t\twidth: 20px;\r\n\t\theight: 20px;\r\n\t\tz-index: 1;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t/* 创建新会话图标更大 */\r\n\t.new-chat-btn .action-icon-img {\r\n\t\twidth: 24px;\r\n\t\theight: 24px;\r\n\t}\r\n\r\n\t/* 移除渐变背景，使用原生图标 */\r\n\t.refresh-btn,\r\n\t.history-btn,\r\n\t.new-chat-btn {\r\n\t\tbackground: transparent;\r\n\t\tbox-shadow: none;\r\n\t}\r\n\r\n\r\n\r\n\t/* 主体滚动区域 */\r\n\t.main-scroll {\r\n\t\tflex: 1;\r\n\t\theight: calc(100vh - 52px - var(--status-bar-height));\r\n\t\tpadding-top: calc(52px + var(--status-bar-height));\r\n\t\tpadding-bottom: 20px;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t/* 区块样式 */\r\n\t.section-block {\r\n\t\tmargin: 10px 15px;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 8px;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.section-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 15px;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t\tbackground-color: #fafafa;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\t.section-arrow {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #909399;\r\n\t\ttransition: transform 0.3s;\r\n\t}\r\n\r\n\t.task-arrow {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #909399;\r\n\t\ttransition: transform 0.3s;\r\n\t\tmargin-right: 8px;\r\n\t}\r\n\r\n\t.close-icon {\r\n\t\tfont-size: 18px;\r\n\t\tcolor: #909399;\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t.section-content {\r\n\t\tpadding: 15px;\r\n\t}\r\n\r\n\t/* AI配置区域 */\r\n\t.ai-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 10px;\r\n\t}\r\n\r\n\t.ai-card {\r\n\t\twidth: calc(50% - 5px);\r\n\t\tborder: 1px solid #ebeef5;\r\n\t\tborder-radius: 8px;\r\n\t\tpadding: 10px;\r\n\t\ttransition: all 0.3s;\r\n\t\tmin-height: 65px;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.ai-card.ai-enabled {\r\n\t\tborder-color: #409EFF;\r\n\t\tbackground-color: #f0f8ff;\r\n\t}\r\n\r\n\t.ai-card.ai-disabled {\r\n\t\tbackground-color: #fafafa;\r\n\t\tborder-color: #e4e7ed;\r\n\t\tborder-style: dashed;\r\n\t\tpointer-events: none;\r\n\t}\r\n\r\n\t.ai-avatar.avatar-disabled {\r\n\t\topacity: 0.7;\r\n\t\tfilter: grayscale(30%);\r\n\t}\r\n\r\n\t.ai-name.name-disabled {\r\n\t\tcolor: #373839;\r\n\t}\r\n\r\n\t.login-required {\r\n\t\tfont-size: 9px;\r\n\t\tcolor: red;\r\n\t\tmargin-top: 2px;\r\n\t\tline-height: 1;\r\n\t}\r\n\r\n\t.loading-text {\r\n\t\tfont-size: 9px;\r\n\t\tcolor: #409EFF;\r\n\t\tmargin-top: 2px;\r\n\t\tline-height: 1;\r\n\t}\r\n\r\n\t.capability-tag.capability-disabled {\r\n\t\topacity: 0.5;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder-color: #e4e7ed;\r\n\t\tpointer-events: none;\r\n\t}\r\n\r\n\t.capability-tag.capability-disabled .capability-text {\r\n\t\tcolor: #c0c4cc;\r\n\t}\r\n\r\n\t.ai-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tmargin-bottom: 8px;\r\n\t\tmin-height: 24px;\r\n\t}\r\n\r\n\t.ai-avatar {\r\n\t\twidth: 24px;\r\n\t\theight: 24px;\r\n\t\tborder-radius: 12px;\r\n\t\tmargin-right: 8px;\r\n\t}\r\n\r\n\t.ai-info {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.ai-name-container {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: flex-start;\r\n\t\tmin-width: 0;\r\n\t}\r\n\r\n\t.ai-name {\r\n\t\tfont-size: 12px;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #303133;\r\n\t\twhite-space: nowrap;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\tmax-width: 100%;\r\n\t}\r\n\r\n\t.ai-capabilities {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 4px;\r\n\t}\r\n\r\n\t.capability-tag {\r\n\t\tpadding: 2px 6px;\r\n\t\tborder-radius: 10px;\r\n\t\tborder: 1px solid #dcdfe6;\r\n\t\tbackground-color: #fff;\r\n\t\ttransition: all 0.3s;\r\n\t}\r\n\r\n\t.capability-tag.capability-active {\r\n\t\tbackground-color: #409EFF;\r\n\t\tborder-color: #409EFF;\r\n\t}\r\n\r\n\t.capability-text {\r\n\t\tfont-size: 10px;\r\n\t\tcolor: #606266;\r\n\t}\r\n\r\n\t.capability-tag.capability-active .capability-text {\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t/* 提示词输入区域 */\r\n\t.prompt-textarea {\r\n\t\twidth: 100%;\r\n\t\tmin-height: 80px;\r\n\t\tpadding: 10px;\r\n\t\tborder: 1px solid #dcdfe6;\r\n\t\tborder-radius: 4px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.5;\r\n\t\tresize: none;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.prompt-footer {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-top: 10px;\r\n\t}\r\n\r\n\t.word-count {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #909399;\r\n\t}\r\n\r\n\t.send-btn {\r\n\t\tbackground-color: #409EFF;\r\n\t\tcolor: #fff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 20px;\r\n\t\tpadding: 6px 0;\r\n\t\tfont-size: 14px;\r\n\t\twidth: 50%;\r\n\t\theight: 30px;\r\n\t\tdisplay: flex;\r\n\t\tmargin-left: 50%;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.send-btn-disabled {\r\n\t\tbackground-color: #c0c4cc;\r\n\t}\r\n\r\n\t/* 任务执行状态 */\r\n\t.task-flow {\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.task-item {\r\n\t\tborder: 1px solid #ebeef5;\r\n\t\tborder-radius: 8px;\r\n\t\tmargin-bottom: 10px;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.task-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 12px;\r\n\t\tbackground-color: #fafafa;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t}\r\n\r\n\t.task-left {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 8px;\r\n\t}\r\n\r\n\t.task-avatar {\r\n\t\twidth: 20px;\r\n\t\theight: 20px;\r\n\t\tborder-radius: 10px;\r\n\t}\r\n\r\n\t.task-name {\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\t.task-right {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 5px;\r\n\t}\r\n\r\n\t.status-text {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #606266;\r\n\t}\r\n\r\n\t.status-icon {\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t.status-completed {\r\n\t\tcolor: #67c23a;\r\n\t}\r\n\r\n\t.status-failed {\r\n\t\tcolor: #f56c6c;\r\n\t}\r\n\r\n\t.status-running {\r\n\t\tcolor: #409EFF;\r\n\t\tanimation: rotate 1s linear infinite;\r\n\t}\r\n\r\n\t.status-idle {\r\n\t\tcolor: #909399;\r\n\t}\r\n\r\n\t@keyframes rotate {\r\n\t\tfrom {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 进度日志 */\r\n\t.progress-logs {\r\n\t\tpadding: 10px 15px;\r\n\t\tmax-height: 150px;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\r\n\t.progress-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tmargin-bottom: 8px;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.progress-item:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.progress-dot {\r\n\t\twidth: 8px;\r\n\t\theight: 8px;\r\n\t\tborder-radius: 4px;\r\n\t\tbackground-color: #dcdfe6;\r\n\t\tmargin-right: 10px;\r\n\t\tmargin-top: 6px;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.progress-dot.dot-completed {\r\n\t\tbackground-color: #67c23a;\r\n\t}\r\n\r\n\t.progress-content {\r\n\t\tflex: 1;\r\n\t\tmin-width: 0;\r\n\t}\r\n\r\n\t.progress-time {\r\n\t\tfont-size: 10px;\r\n\t\tcolor: #909399;\r\n\t\tmargin-bottom: 2px;\r\n\t}\r\n\r\n\t.progress-text {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #606266;\r\n\t\tline-height: 1.4;\r\n\t\tword-break: break-all;\r\n\t}\r\n\r\n\t/* 主机可视化 */\r\n\t.screenshots-section {\r\n\t\tmargin-top: 15px;\r\n\t}\r\n\r\n\t.screenshots-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 10px;\r\n\t\tgap: 10px;\r\n\t}\r\n\r\n\t.section-subtitle {\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\t.auto-play-text {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #606266;\r\n\t}\r\n\r\n\t.screenshots-swiper {\r\n\t\theight: 200px;\r\n\t\tborder-radius: 8px;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.screenshot-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t/* 结果展示区域 - 简洁标签页风格 */\r\n\r\n\t.result-tabs {\r\n\t\twhite-space: nowrap;\r\n\t\tmargin-bottom: 20px;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t}\r\n\r\n\t.tab-container {\r\n\t\tdisplay: flex;\r\n\t\tgap: 0;\r\n\t\tpadding: 0 15px;\r\n\t}\r\n\r\n\t.result-tab {\r\n\t\tflex-shrink: 0;\r\n\t\tpadding: 12px 20px;\r\n\t\tposition: relative;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tbackground: transparent;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.result-tab::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tleft: 50%;\r\n\t\twidth: 0;\r\n\t\theight: 2px;\r\n\t\tbackground: #409EFF;\r\n\t\ttransition: all 0.3s ease;\r\n\t\ttransform: translateX(-50%);\r\n\t}\r\n\r\n\t.result-tab.tab-active::after {\r\n\t\twidth: 80%;\r\n\t}\r\n\r\n\t.tab-text {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #909399;\r\n\t\tfont-weight: 400;\r\n\t\ttransition: all 0.3s ease;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.result-tab.tab-active .tab-text {\r\n\t\tcolor: #409EFF;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.result-tab:active {\r\n\t\ttransform: translateY(1px);\r\n\t}\r\n\r\n\t.result-content {\r\n\t\tmargin-top: 10px;\r\n\t}\r\n\r\n\t.result-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 10px;\r\n\t\tpadding-bottom: 8px;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t}\r\n\r\n\t.result-title {\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\r\n\r\n\t.result-body {\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.result-image-container {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.result-image {\r\n\t\tmax-width: 100%;\r\n\t\tborder-radius: 8px;\r\n\t}\r\n\r\n\t/* PDF文件容器样式 */\r\n\t.result-pdf-container {\r\n\t\tbackground-color: #f9f9f9;\r\n\t\tborder-radius: 8px;\r\n\t\tborder: 2px dashed #dcdfe6;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.pdf-placeholder {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tpadding: 20px;\r\n\t}\r\n\r\n\t.pdf-icon {\r\n\t\tfont-size: 48px;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.pdf-text {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #606266;\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.pdf-actions {\r\n\t\tdisplay: flex;\r\n\t\tgap: 10px;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.pdf-btn {\r\n\t\tborder-radius: 4px;\r\n\t\tpadding: 8px 16px;\r\n\t\tfont-size: 12px;\r\n\t\theight: auto;\r\n\t\tline-height: 1.2;\r\n\t\tflex: 1;\r\n\t\tmax-width: 100px;\r\n\t}\r\n\r\n\t.download-btn {\r\n\t\tbackground-color: #f6ffed;\r\n\t\tcolor: #52c41a;\r\n\t\tborder: 1px solid #b7eb8f;\r\n\t}\r\n\r\n\t.copy-btn {\r\n\t\tbackground-color: #fff7e6;\r\n\t\tcolor: #fa8c16;\r\n\t\tborder: 1px solid #ffd591;\r\n\t}\r\n\r\n\t.result-text {\r\n\t\tpadding: 10px;\r\n\t\tbackground-color: #f9f9f9;\r\n\t\tborder-radius: 8px;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 1.6;\r\n\t\tmax-height: 300px;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\r\n\t.result-actions {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: flex-end;\r\n\t\tgap: 8px;\r\n\t\tflex-wrap: wrap;\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.action-btn-small, .share-link-btn, .collect-btn {\r\n\t\tborder: 1px solid #dcdfe6;\r\n\t\tborder-radius: 12px;\r\n\t\tpadding: 4px 12px;\r\n\t\tfont-size: 12px;\r\n\t\theight: auto;\r\n\t\tline-height: 1.2;\r\n\t\tmin-width: 60px;\r\n\t\ttext-align: center;\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.action-btn-small {\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tcolor: #606266;\r\n\t\tborder-color: #dcdfe6;\r\n\t}\r\n\r\n\t.share-link-btn {\r\n\t\tbackground-color: #67c23a;\r\n\t\tcolor: #fff;\r\n\t\tborder-color: #67c23a;\r\n\t}\r\n\r\n\t.collect-btn {\r\n\t\tbackground-color: #e6a23c;\r\n\t\tcolor: #fff;\r\n\t\tborder-color: #e6a23c;\r\n\t}\r\n\r\n\t/* 按钮悬停和点击效果 */\r\n\t.action-btn-small:active {\r\n\t\topacity: 0.8;\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t.share-link-btn:active {\r\n\t\topacity: 0.8;\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t.collect-btn:active {\r\n\t\topacity: 0.8;\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t/* 智能评分按钮在标题栏 */\r\n\t.score-btn {\r\n\t\tbackground-color: #409EFF;\r\n\t\tcolor: #fff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 12px;\r\n\t\tpadding: 4px 12px;\r\n\t\tfont-size: 12px;\r\n\t\theight: auto;\r\n\t\tline-height: 1.2;\r\n\t\tmargin-left: 57%;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t/* 历史记录抽屉 */\r\n\t.drawer-mask {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tz-index: 999;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: flex-end;\r\n\t}\r\n\r\n\t.drawer-container {\r\n\t\twidth: 280px;\r\n\t\theight: 100vh;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.drawer-content {\r\n\t\tmargin-top: 120rpx;\r\n\t\theight: 100vh;\r\n\t\tbackground-color: #fff;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.drawer-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 15px;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t}\r\n\r\n\t.drawer-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\t.history-list {\r\n\t\tflex: 1;\r\n\t\tpadding: 10px;\r\n\t\theight: calc(100vh - 60px);\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.history-group {\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.history-date {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #909399;\r\n\t\tmargin-bottom: 8px;\r\n\t\tpadding: 5px 0;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.history-item {\r\n\t\tbackground-color: #f9f9f9;\r\n\t\tborder-radius: 8px;\r\n\t\tpadding: 10px;\r\n\t\tmargin-bottom: 8px;\r\n\t}\r\n\r\n\t.history-prompt {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #303133;\r\n\t\tline-height: 1.4;\r\n\t\tmargin-bottom: 5px;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-line-clamp: 2;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.history-time {\r\n\t\tfont-size: 10px;\r\n\t\tcolor: #909399;\r\n\t}\r\n\r\n\t/* 智能评分弹窗 */\r\n\t.popup-mask {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tz-index: 999;\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-end;\r\n\t}\r\n\r\n\t.score-modal {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 20px 20px 0 0;\r\n\t\tmax-height: 80vh;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.score-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 15px;\r\n\t\tborder-bottom: 1px solid #ebeef5;\r\n\t}\r\n\r\n\t.score-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #303133;\r\n\t}\r\n\r\n\t.score-content {\r\n\t\tflex: 1;\r\n\t\tpadding: 15px;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\r\n\t.score-selection {\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\r\n\t.score-subtitle {\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #303133;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.score-checkboxes {\r\n\t\tmargin-top: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 8px;\r\n\t}\r\n\r\n\t.checkbox-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 8px;\r\n\t}\r\n\r\n\t.checkbox-text {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #606266;\r\n\t}\r\n\r\n\t.score-prompt-section {\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n\r\n\t.score-textarea {\r\n\t\twidth: 100%;\r\n\t\tmin-height: 120px;\r\n\t\tmax-height: 300px;\r\n\t\tpadding: 10px;\r\n\t\tborder: 1px solid #dcdfe6;\r\n\t\tborder-radius: 8px;\r\n\t\tfont-size: 14px;\r\n\t\tresize: vertical;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-top: 10px;\r\n\t\tword-wrap: break-word;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\r\n\t/* 微头条文章内容超过2000字时的样式 */\r\n\t.score-textarea.content-exceeded {\r\n\t\tborder-color: #f56c6c;\r\n\t\tbackground-color: #fef0f0;\r\n\t}\r\n\r\n\t/* 字符计数样式 */\r\n\t.char-count {\r\n\t\ttext-align: right;\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #909399;\r\n\t\tmargin-top: 5px;\r\n\t}\r\n\r\n\t/* 字符计数超过限制时的样式 */\r\n\t.char-count-exceeded {\r\n\t\tcolor: #f56c6c;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.score-submit-btn {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #409EFF;\r\n\t\tcolor: #fff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 8px;\r\n\t\tpadding: 12px;\r\n\t\tfont-size: 16px;\r\n\t}\r\n\r\n\t.score-submit-btn[disabled] {\r\n\t\tbackground-color: #c0c4cc;\r\n\t}\r\n\r\n\t/* 响应式布局 */\r\n\t@media (max-width: 375px) {\r\n\t\t.ai-card {\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\t.header-content {\r\n\t\t\tpadding: 8px 12px;\r\n\t\t}\r\n\r\n\t\t.section-block {\r\n\t\t\tmargin: 8px 12px;\r\n\t\t}\r\n\t}\r\n\r\n\t/* 响应式布局 */\r\n\t@media (max-width: 375px) {\r\n\t\t.ai-card {\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\t.header-content {\r\n\t\t\tpadding: 8px 12px;\r\n\t\t}\r\n\r\n\t\t.section-block {\r\n\t\t\tmargin: 8px 12px;\r\n\t\t}\r\n\t}\r\n\r\n  /* DeepSeek响应内容的特定样式 */\r\n  .deepseek-format-container {\r\n    margin: 20px 0;\r\n    padding: 15px;\r\n    background-color: #f9f9f9;\r\n    border-radius: 5px;\r\n    border: 1px solid #eaeaea;\r\n  }\r\n\r\n  .result-text .deepseek-response {\r\n    max-width: 100%;\r\n    margin: 0 auto;\r\n    background-color: #fff;\r\n    border-radius: 8px;\r\n    padding: 10px;\r\n    font-family: Arial, sans-serif;\r\n  }\r\n\r\n  .result-text .deepseek-response pre {\r\n    background-color: #f5f5f5;\r\n    padding: 10px;\r\n    border-radius: 4px;\r\n    font-family: monospace;\r\n    overflow-x: auto;\r\n    display: block;\r\n    margin: 10px 0;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .result-text .deepseek-response code {\r\n    background-color: #f5f5f5;\r\n    padding: 2px 4px;\r\n    border-radius: 3px;\r\n    font-family: monospace;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .result-text .deepseek-response table {\r\n    border-collapse: collapse;\r\n    width: 100%;\r\n    margin: 15px 0;\r\n  }\r\n\r\n  .result-text .deepseek-response th,\r\n  .result-text .deepseek-response td {\r\n    border: 1px solid #ddd;\r\n    padding: 8px;\r\n    text-align: left;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .result-text .deepseek-response th {\r\n    background-color: #f2f2f2;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .result-text .deepseek-response h1,\r\n  .result-text .deepseek-response h2,\r\n  .result-text .deepseek-response h3,\r\n  .result-text .deepseek-response h4,\r\n  .result-text .deepseek-response h5,\r\n  .result-text .deepseek-response h6 {\r\n    margin-top: 20px;\r\n    margin-bottom: 10px;\r\n    font-weight: bold;\r\n    color: #222;\r\n  }\r\n\r\n  .result-text .deepseek-response a {\r\n    color: #0066cc;\r\n    text-decoration: none;\r\n  }\r\n\r\n  .result-text .deepseek-response blockquote {\r\n    border-left: 4px solid #ddd;\r\n    padding-left: 15px;\r\n    margin: 15px 0;\r\n    color: #555;\r\n  }\r\n\r\n  .result-text .deepseek-response ul,\r\n  .result-text .deepseek-response ol {\r\n    padding-left: 20px;\r\n    margin: 10px 0;\r\n  }\r\n\r\n  /* 媒体选择样式 */\r\n  .media-selection-section {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .media-radio-group {\r\n    display: flex;\r\n    gap: 10px;\r\n    margin: 10px 0;\r\n  }\r\n\r\n  .media-radio-item {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    padding: 15px 10px;\r\n    border: 1px solid #e0e0e0;\r\n    border-radius: 8px;\r\n    background-color: #f9f9f9;\r\n    transition: all 0.3s ease;\r\n  }\r\n\r\n  .media-radio-item.active {\r\n    border-color: #409eff;\r\n    background-color: #ecf5ff;\r\n  }\r\n\r\n  .media-icon {\r\n    font-size: 24px;\r\n    margin-bottom: 5px;\r\n  }\r\n\r\n  .media-text {\r\n    font-size: 14px;\r\n    color: #333;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .media-description {\r\n    margin-top: 10px;\r\n    padding: 8px 12px;\r\n    background-color: #f0f9ff;\r\n    border-radius: 4px;\r\n    border-left: 3px solid #409eff;\r\n  }\r\n\r\n  .description-text {\r\n    font-size: 12px;\r\n    color: #666;\r\n    line-height: 1.4;\r\n  }\r\n\r\n  /* 微头条按钮样式 */\r\n  .media-radio-item.active {\r\n    background: linear-gradient(135deg, #ff6b35, #f7931e);\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=51b5538d&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=51b5538d&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754060939159\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}