{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/mine/index.vue?aa94", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/mine/index.vue?7a56", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/mine/index.vue?9fb0", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/mine/index.vue?c2cd", "uni-app:///pages/mine/index.vue", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/mine/index.vue?1423", "webpack:///D:/LanguageProjects/u3w-ai/U3W-AI/cube-mini/pages/mine/index.vue?a9ec"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "wxLoginForm", "code", "qwcode", "encryptedIv", "encryptedData", "nick<PERSON><PERSON>", "avatar", "name", "openid", "userid", "points", "reportNum", "collectionNum", "browseNum", "version", "onLoad", "computed", "windowHeight", "onShow", "console", "created", "methods", "getUserCount", "wx<PERSON><PERSON><PERSON><PERSON><PERSON>", "uni", "url", "handleToInfo", "handleToEditInfo", "subscribeRes", "handleToLogin", "handleToAvatar", "handleLogout", "myPoints"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgoB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACsDppB;AACA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;EAAA,CACA;EACAC;IACAV;MACA;IACA;IACAW;MACA;IACA;EACA;EACAC;IACAC;IACA;MACA;IACA;EACA;EACAC,6BAEA;EACAC;IACAC;MAAA;MAEA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACAC;QACAC;MACA;IAGA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACvKA;AAAA;AAAA;AAAA;AAAmsC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAvtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/mine/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4bd6864f&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4bd6864f&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"mine-container\" :style=\"{height: `${windowHeight}px`}\">\r\n    <!--顶部个人信息栏-->\r\n    <view class=\"header-section\">\r\n      <view class=\"flex justify-between\" style=\"padding: 0 30rpx 50rpx;\">\r\n        <view class=\"flex align-center\">\r\n\r\n          <image v-if=\"avatar\" :src=\"avatar\" class=\"cu-avatar xl round\" mode=\"widthFix\"></image>\r\n          <view v-if=\"!name\" @click=\"wxhandleLogin\" class=\"login-tip\">\r\n            点击登录\r\n          </view>\r\n          <view v-if=\"name\" class=\"user-info\">\r\n            <view class=\"u_title\">\r\n              {{ name }}\r\n            </view>\r\n            <view class=\"u_title1\">\r\n              用户ID: {{ userid }}\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"content-section\">\r\n     <view class=\"mine-actions grid col-4 text-center\">\r\n        <view class=\"action-item\" @click=\"myPoints\">\r\n          <text style=\"font-size: 16px;\"> {{points}} </text>\r\n          <text class=\"text\">我的积分</text>\r\n        </view>\r\n      \r\n      </view>\r\n\r\n      <view class=\"menu-list\">\r\n        <view class=\"list-cell list-cell-arrow\" @click=\"handleToEditInfo\">\r\n          <view class=\"menu-item-box\">\r\n            <view class=\"iconfont icon-user menu-icon\"></view>\r\n            <view>完善资料</view>\r\n          </view>\r\n        </view>\r\n\r\n      </view>\r\n\t  \r\n\r\n      <view class=\"action-btn\" v-if=\"name\">\r\n        <button @click=\"handleLogout\" class=\"login-btn cu-btn block bg-green lg round\">退出登录</button>\r\n      </view>\r\n\r\n\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import storage from '@/utils/storage'\r\n  import {\r\n    getUserCount\r\n  } from '@/api/report'\r\n  import constant from '@/utils/constant'\r\n\r\n  export default {\r\n    data() {\r\n      return {\r\n        /**微信登录的form数据**/\r\n        wxLoginForm: {\r\n          code: \"\",\r\n          qwcode: \"\",\r\n          encryptedIv: \"\",\r\n          encryptedData: \"\",\r\n          nickName: \"\",\r\n          avatar: \"\"\r\n        },\r\n\r\n        name: storage.get(constant.name),\r\n        openid: this.$store.state.user.openid,\r\n        userid: storage.get(constant.userId),\r\n        points: 0,\r\n        reportNum: 0,\r\n        collectionNum: 0,\r\n        browseNum: 0,\r\n        version: getApp().globalData.config.appInfo.version\r\n      }\r\n    },\r\n\tonLoad(options){\r\n\t//   if (options) {\r\n\t//     let orderId = decodeURIComponent(options.orderId)\r\n\t// \tif(orderId){\r\n\t// \t\tuni.showModal({\r\n\t// \t\t    title: '嘟嘟',\r\n\t// \t\t    content: '订单ID:'+orderId,\r\n\t// \t\t    success: function (res) {\r\n\t// \t\t        if (res.confirm) {\r\n\t// \t\t            console.log('用户点击确定');\r\n\t// \t\t            // 用户点击了确定按钮的相关逻辑可以放在这里\r\n\t// \t\t        } else if (res.cancel) {\r\n\t// \t\t            console.log('用户点击取消');\r\n\t// \t\t            // 用户点击了取消按钮的相关逻辑可以放在这里\r\n\t// \t\t        }\r\n\t// \t\t    }\r\n\t// \t\t});\r\n\t// \t}\r\n\t\r\n\t//   }\r\n\t},\r\n    computed: {\r\n      avatar() {\r\n        return storage.get(constant.avatar)\r\n      },\r\n      windowHeight() {\r\n        return uni.getSystemInfoSync().windowHeight - 50\r\n      }\r\n    },\r\n    onShow() {\r\n      console.log(\"用户ID\" + this.userid)\r\n      if (this.userid) {\r\n        this.getUserCount(this.userid)\r\n      }\r\n    },\r\n    created() {\r\n\r\n    },\r\n    methods: {\r\n      getUserCount(userid) {\r\n\r\n        getUserCount(userid).then(res => {\r\n          this.points = res.data.points\r\n          this.reportNum = res.data.reportNum\r\n          this.collectionNum = res.data.collectionNum\r\n          this.browseNum = res.data.browseNum\r\n        })\r\n      },\r\n      wxhandleLogin() {\r\n        uni.navigateTo({\r\n          url: '/pages/login/index'\r\n        });\r\n\r\n\r\n      },\r\n\r\n      handleToInfo() {\r\n        this.$tab.navigateTo('/pages/mine/info/index')\r\n      },\r\n      handleToEditInfo() {\r\n        this.$tab.navigateTo('/pages/mine/info/edit')\r\n      },\r\n\r\n      subscribeRes() {\r\n        this.$tab.navigateTo('/pages/mine/setting/index')\r\n      },\r\n      handleToLogin() {\r\n        this.$tab.reLaunch('/pages/login')\r\n      },\r\n      handleToAvatar() {\r\n        this.$tab.navigateTo('/pages/mine/avatar/index')\r\n      },\r\n      handleLogout() {\r\n        this.$modal.confirm('确定退出登录吗？').then(() => {\r\n          this.$store.dispatch('LogOut').then(() => {\r\n            this.$tab.reLaunch('/pages/mine/index')\r\n          })\r\n        })\r\n      },\r\n      myPoints() {\r\n        this.$tab.navigateTo('/pages/user/points/index')\r\n      }\r\n    \r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n  page {\r\n    background-color: #f5f6f7;\r\n  }\r\n\r\n  .login-btn {\r\n    margin-top: 40px;\r\n    height: 90rpx !important;\r\n    line-height: 90rpx;\r\n    margin-left: 5%;\r\n    width: 90%;\r\n  }\r\n\r\n  .mine-container {\r\n    width: 100%;\r\n    height: 100%;\r\n\r\n\r\n    .header-section {\r\n      padding: 10rpx 20rpx 70rpx;\r\n      background-color: #1ec57c;\r\n      // background-color: #3c96f3;\r\n      color: white;\r\n\r\n      .login-tip {\r\n        font-size: 18px;\r\n        margin-left: 10px;\r\n      }\r\n\r\n      .cu-avatar {\r\n        // border: 2px solid #eaeaea;\r\n        width: 120rpx;\r\n        height: 120rpx;\r\n\r\n        .icon {\r\n          font-size: 40px;\r\n        }\r\n      }\r\n\r\n      .user-info {\r\n        margin-left: 15px;\r\n\r\n        .u_title {\r\n          font-size: 38rpx;\r\n          margin-bottom: 6rpx;\r\n        }\r\n\r\n        .u_title1 {\r\n          font-size: 24rpx;\r\n        }\r\n      }\r\n    }\r\n\r\n    .content-section {\r\n      position: relative;\r\n      top: -50px;\r\n\r\n      .mine-actions {\r\n        margin: 30rpx 30rpx;\r\n        padding: 40rpx 0 28rpx;\r\n        border-radius: 16rpx;\r\n        background-color: white;\r\n\r\n        .action-item {\r\n          .icon {\r\n            font-size: 48rpx;\r\n          }\r\n\r\n          .text {\r\n            display: block;\r\n            font-size: 26rpx;\r\n            margin: 16rpx 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754060940177\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}